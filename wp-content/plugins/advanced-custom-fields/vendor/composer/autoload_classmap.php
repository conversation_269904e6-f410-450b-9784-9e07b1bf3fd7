<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'ACF\\Blocks\\Bindings' => $baseDir . '/src/Blocks/Bindings.php',
    'ACF\\Meta\\Comment' => $baseDir . '/src/Meta/Comment.php',
    'ACF\\Meta\\MetaLocation' => $baseDir . '/src/Meta/MetaLocation.php',
    'ACF\\Meta\\Option' => $baseDir . '/src/Meta/Option.php',
    'ACF\\Meta\\Post' => $baseDir . '/src/Meta/Post.php',
    'ACF\\Meta\\Term' => $baseDir . '/src/Meta/Term.php',
    'ACF\\Meta\\User' => $baseDir . '/src/Meta/User.php',
    'ACF\\Site_Health\\Site_Health' => $baseDir . '/src/Site_Health/Site_Health.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
);

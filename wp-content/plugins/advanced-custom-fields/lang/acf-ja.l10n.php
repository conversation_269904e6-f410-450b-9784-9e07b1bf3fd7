<?php
return ['domain'=>NULL,'plural-forms'=>NULL,'language'=>'ja','project-id-version'=>'Advanced Custom Fields','pot-creation-date'=>'2025-05-08T10:32:33+00:00','po-revision-date'=>'2025-05-08T10:20:52+00:00','x-generator'=>'gettext','messages'=>['By default only admin users can edit this setting.'=>'デフォルトでは管理者ユーザーのみがこの設定を編集できます。','By default only super admin users can edit this setting.'=>'デフォルトでは特権管理者ユーザーのみがこの設定を編集できます。','[The ACF shortcode is disabled on this site]'=>'[ACFショートコードはこのサイトでは無効化されています]','Businessman Icon'=>'ビジネスマンアイコン','Forums Icon'=>'フォーラムアイコン','YouTube Icon'=>'YouTube アイコン','Xing Icon'=>'Xing アイコン','WordPress (alt) Icon'=>'WordPress (alt) アイコン','WhatsApp Icon'=>'WhatsApp アイコン','Widgets Menus Icon'=>'ウィジェットメニューアイコン','View Site Icon'=>'サイト表示アイコン','Learn More Icon'=>'詳細アイコン','Add Page Icon'=>'ページ追加アイコン','Video (alt3) Icon'=>'動画 (alt3) アイコン','Video (alt2) Icon'=>'動画 (alt2) アイコン','Video (alt) Icon'=>'動画 (alt) アイコン','Twitter (alt) Icon'=>'Twitter (alt) アイコン','Tickets (alt) Icon'=>'チケット (alt) アイコン','Text Page Icon'=>'テキストページアイコン','Spotify Icon'=>'Spotify アイコン','Shortcode Icon'=>'ショートコードアイコン','Saved Icon'=>'保存アイコン','RSS Icon'=>'RSS アイコン','REST API Icon'=>'REST API アイコン','Remove Icon'=>'削除アイコン','Reddit Icon'=>'Reddit アイコン','Privacy Icon'=>'プライバシーアイコン','Printer Icon'=>'プリンターアイコン','Podio Icon'=>'Podioアイコン','Plus (alt) Icon'=>'プラス (alt) アイコン','Pinterest Icon'=>'Pinterest アイコン','Pets Icon'=>'ペットアイコン','PDF Icon'=>'PDF アイコン','Palm Tree Icon'=>'ヤシの木アイコン','Spreadsheet Icon'=>'スプレッドシート アイコン','Interactive Icon'=>'対話アイコン','Document Icon'=>'ドキュメントアイコン','Default Icon'=>'デフォルトアイコン','Location (alt) Icon'=>'ロケーション (alt) アイコン','LinkedIn Icon'=>'LinkedIn アイコン','Instagram Icon'=>'Instagram アイコン','Rotate Left Icon'=>'左回転アイコン','Flip Horizontal Icon'=>'水平に反転アイコン','Crop Icon'=>'切り抜きアイコン','ID (alt) Icon'=>'ID (alt) アイコン','HTML Icon'=>'HTML アイコン','Hourglass Icon'=>'砂時計アイコン','Heading Icon'=>'見出しアイコン','Google Icon'=>'Google アイコン','Status Icon'=>'ステータスアイコン','Image Icon'=>'画像アイコン','Gallery Icon'=>'ギャラリーアイコン','Chat Icon'=>'チャットアイコン','Audio Icon'=>'オーディオアイコン','bbPress Icon'=>'BbPress アイコン','Block Default Icon'=>'ブロックデフォルトアイコン','Align Pull Right Icon'=>'右揃えアイコン','Align Pull Left Icon'=>'左揃えアイコン','Invalid request args.'=>'無効なリクエストです。','Sorry, you do not have permission to do that.'=>'その操作を行う権限がありません。','Blocks Using Post Meta'=>'投稿メタを使用したブロック','ACF PRO logo'=>'ACF PRO ロゴ','The value of icon to save.'=>'保存するアイコンの値。','The type of icon to save.'=>'保存するアイコンのタイプ。','Yes Icon'=>'承認アイコン','WordPress Icon'=>'WordPress アイコン','Translation Icon'=>'翻訳アイコン','Microphone Icon'=>'マイクアイコン','Marker Icon'=>'マーカーアイコン','Lock Icon'=>'ロックアイコン','Location Icon'=>'ロケーションアイコン','Lightbulb Icon'=>'電球アイコン','Layout Icon'=>'レイアウトアイコン','Laptop Icon'=>'ラップトップアイコン','Info Icon'=>'情報アイコン','ID Icon'=>'IDアイコン','Hidden Icon'=>'非表示アイコン','Heart Icon'=>'ハートアイコン','Hammer Icon'=>'ハンマーアイコン','Groups Icon'=>'グループアイコン','Grid View Icon'=>'グリッドビューアイコン','Forms Icon'=>'フォームアイコン','Flag Icon'=>'旗アイコン','Filter Icon'=>'フィルターアイコン','Feedback Icon'=>'フィードバックアイコン','Facebook Icon'=>'Facebook アイコン','External Icon'=>'外部アイコン','Email Icon'=>'メールアイコン','Video Icon'=>'動画アイコン','Unlink Icon'=>'リンク解除アイコン','Underline Icon'=>'下線アイコン','Text Color Icon'=>'文字の色アイコン','Table Icon'=>'テーブルアイコン','Strikethrough Icon'=>'打ち消しアイコン','Spellcheck Icon'=>'スペルチェックアイコン','Quote Icon'=>'引用アイコン','Paste Text Icon'=>'テキスト貼り付けアイコン','Paragraph Icon'=>'段落アイコン','Outdent Icon'=>'アウトデントアイコン','Italic Icon'=>'イタリックアイコン','Insert More Icon'=>'挿入アイコン','Indent Icon'=>'インデントアイコン','Help Icon'=>'ヘルプアイコン','Code Icon'=>'コードアイコン','Break Icon'=>'改行アイコン','Bold Icon'=>'太字アイコン','Edit Icon'=>'編集アイコン','Download Icon'=>'ダウンロードアイコン','Dismiss Icon'=>'閉じるアイコン','Desktop Icon'=>'デスクトップアイコン','Dashboard Icon'=>'ダッシュボードアイコン','Carrot Icon'=>'にんじんアイコン','Post Icon'=>'投稿アイコン','Registered Post Types (JSON)'=>'登録された投稿タイプ (JSON)','Term is equal to'=>'タームが一致する','Has no user selected'=>'ユーザーが選択されていません','User is not equal to'=>'ユーザーが一致しない','Post is equal to'=>'投稿が以下に等しい場合','ACF PRO Feature'=>'ACF PRO 機能','Please contact your site administrator or developer for more details.'=>'詳細については、サイト管理者または開発者にお問い合わせください。','Learn&nbsp;more'=>'さらに詳しく','Hide&nbsp;details'=>'詳細を隠す','Show&nbsp;details'=>'詳細を表示','Renew ACF PRO License'=>'ACF プロ版を更新する','Renew License'=>'ライセンスを更新','Manage License'=>'ライセンスの管理','\'High\' position not supported in the Block Editor'=>'位置「高」はブロックエディタ―ではサポートされていません','Upgrade to ACF PRO'=>'ACF プロ版へアップグレード','Add Options Page'=>'オプションページを追加','In the editor used as the placeholder of the title.'=>'タイトルのプレースホルダーとして使用されます。','Title Placeholder'=>'タイトルプレースホルダー','4 Months Free'=>'4ヶ月無料','(Duplicated from %s)'=>'( %s からの複製)','Select Options Pages'=>'オプションページを選択','Duplicate taxonomy'=>'タクソノミーを複製','Create taxonomy'=>'タクソノミーを作成','Duplicate post type'=>'投稿タイプを複製','Create post type'=>'投稿タイプを作成','Link field groups'=>'フィールドグループ','Add fields'=>'フィールドを追加','This Field'=>'このフィールド','ACF PRO'=>'ACF PRO','Feedback'=>'フィードバック','Support'=>'サポート','is developed and maintained by'=>'によって開発され、維持されている','Add this %s to the location rules of the selected field groups.'=>'%s を選択したフィールドグループのロケーションルールに追加します。','Target Field'=>'ターゲットフィールド','Bidirectional'=>'双方向','%s Field'=>'%s フィールド','Select Multiple'=>'複数選択','WP Engine logo'=>'WP Engine ロゴ','Lower case letters, underscores and dashes only, Max 32 characters.'=>'小文字、アンダースコア、ダッシュのみ、最大32文字','The capability name for assigning terms of this taxonomy.'=>'投稿などでこのタクソノミーのタームを設定するための権限','Assign Terms Capability'=>'ターム割り当て','The capability name for deleting terms of this taxonomy.'=>'このタクソノミーのタームを削除するための権限','Delete Terms Capability'=>'ターム削除機能','The capability name for editing terms of this taxonomy.'=>'このタクソノミーのタームを編集するための権限','Edit Terms Capability'=>'ターム編集機能','The capability name for managing terms of this taxonomy.'=>'このタクソノミーの管理画面へのアクセスを許可する権限','Manage Terms Capability'=>'ターム管理機能','Sets whether posts should be excluded from search results and taxonomy archive pages.'=>'サイト内検索やタクソノミーアーカイブから投稿を除外するかどうかを設定。','More Tools from WP Engine'=>'WP Engine 他のツール','Built for those that build with WordPress, by the team at %s'=>'%s のチームによって、WordPressで構築する人々のために構築された','View Pricing & Upgrade'=>'価格とアップグレードを見る','Learn More'=>'さらに詳しく','%s fields'=>'%s フィールド','No terms'=>'タームはありません','No post types'=>'投稿タイプはありません','No posts'=>'投稿はありません','No taxonomies'=>'タクソノミーはありません','No field groups'=>'フィールドグループはありません','No fields'=>'フィールドはありません','No description'=>'説明はありません','Any post status'=>'すべての投稿ステータス','This taxonomy key is already in use by another taxonomy in ACF and cannot be used.'=>'このタクソノミーのキーはすでに ACF の他のタクソノミーで使用されているので使用できません。','The taxonomy key must only contain lower case alphanumeric characters, underscores or dashes.'=>'タクソノミーのキーは小文字の英数字、アンダースコア、またはダッシュのみが含まれます。','The taxonomy key must be under 32 characters.'=>'タクソノミーのキーは32字以内にする必要があります。','No Taxonomies found in Trash'=>'ゴミ箱内にタクソノミーが見つかりませんでした。','No Taxonomies found'=>'タクソノミーが見つかりません','Search Taxonomies'=>'タクソノミーを検索','View Taxonomy'=>'タクソノミーを表示','New Taxonomy'=>'新規タクソノミー','Edit Taxonomy'=>'タクソノミーを編集','Add New Taxonomy'=>'新規タクソノミーを追加','No Post Types found in Trash'=>'ゴミ箱内に投稿タイプが見つかりませんでした。','No Post Types found'=>'投稿タイプが見つかりません。','Search Post Types'=>'投稿タイプを検索','View Post Type'=>'投稿タイプを表示','New Post Type'=>'新規投稿タイプ','Edit Post Type'=>'投稿タイプを編集','Add New Post Type'=>'新規投稿タイプを追加','This post type key is already in use by another post type registered outside of ACF and cannot be used.'=>'この投稿タイプキーは、ACF の外側で登録された別の投稿タイプによってすでに使用されているため、使用できません。','This post type key is already in use by another post type in ACF and cannot be used.'=>'この投稿タイプキーは、ACF の別の投稿タイプによってすでに使用されているため、使用できません。','The post type key must be under 20 characters.'=>'投稿タイプのキーは20字以内にする必要があります。','We do not recommend using this field in ACF Blocks.'=>'このフィールドの ACF ブロックでの使用は推奨されません。','WYSIWYG Editor'=>'リッチ エディター (WYSIWYG)','Allows the selection of one or more users which can be used to create relationships between data objects.'=>'データオブジェクト間のリレーションシップを作成するために使用できる、1人または複数のユーザーを選択できます。','A text input specifically designed for storing web addresses.'=>'ウェブアドレスを保存するために特別に設計されたテキスト入力。','URL'=>'URL','A basic textarea input for storing paragraphs of text.'=>'テキストの段落を保存するための標準的な入力テキストエリア。','A basic text input, useful for storing single string values.'=>'基本的なテキスト入力で、単一の文字列値を格納するのに便利です。','Filter by Post Status'=>'投稿ステータスで絞り込む','An input limited to numerical values.'=>'数値のみの入力','Provides a way to structure fields into groups to better organize the data and the edit screen.'=>'データと編集画面をよりよく整理するために、フィールドをグループに構造化する方法を提供します。','A text input specifically designed for storing email addresses.'=>'メールアドレスを保存するために特別に設計されたテキスト入力。','A group of checkbox inputs that allow the user to select one, or multiple values that you specify.'=>'ユーザーが1つ、または指定した複数の値を選択できるチェックボックス入力のグループ。','A group of buttons with values that you specify, users can choose one option from the values provided.'=>'あなたが指定した値を持つボタンのグループ、ユーザーは提供された値から1つのオプションを選択することができます。','Allows you to group and organize custom fields into collapsable panels that are shown while editing content. Useful for keeping large datasets tidy.'=>'コンテンツの編集中に表示される折りたたみ可能なパネルに、カスタムフィールドをグループ化して整理することができます。大規模なデータセットを整理整頓するのに便利です。','This provides a solution for repeating content such as slides, team members, and call-to-action tiles, by acting as a parent to a set of subfields which can be repeated again and again.'=>'これは、スライド、チームメンバー、行動喚起表示などのコンテンツを繰り返し表示するためのソリューションで、繰り返し表示できる一連のサブフィールドの親として機能します。','This provides an interactive interface for managing a collection of attachments. Most settings are similar to the Image field type. Additional settings allow you to specify where new attachments are added in the gallery and the minimum/maximum number of attachments allowed.'=>'これは、添付ファイルのコレクションを管理するためのインタラクティブなインターフェイスを提供します。ほとんどの設定は画像フィールドタイプと似ています。追加設定では、ギャラリーで新しい添付ファイルを追加する場所と、許可される添付ファイルの最小/最大数を指定できます。','nounClone'=>'複製','PRO'=>'PRO','Advanced'=>'高度','JSON (newer)'=>'JSON (新しい)','Original'=>'オリジナル','Invalid post ID.'=>'無効な投稿 ID です。','Invalid post type selected for review.'=>'レビューには無効な投稿タイプが選択されています。','More'=>'詳細','Tutorial'=>'チュートリアル','Select Field'=>'フィールド選択','Popular fields'=>'よく使うフィールド','No search results for \'%s\''=>'「%s」に合う検索結果はありません','Search fields...'=>'フィールドを検索...','Select Field Type'=>'フィールドタイプを選択する','Popular'=>'人気','Add Taxonomy'=>'タクソノミーを追加','Add Your First Taxonomy'=>'最初のタクソノミーを追加','genre'=>'ジャンル','Genre'=>'ジャンル','Genres'=>'ジャンル','Expose this post type in the REST API.'=>'この投稿タイプをREST APIで公開する。','Customize the query variable name'=>'クエリ変数名をカスタマイズ','Parent-child terms in URLs for hierarchical taxonomies.'=>'このタクソノミーが親子関係を持つかどうか。','Customize the slug used in the URL'=>'URLに使用されるスラッグをカスタマイズする','Permalinks for this taxonomy are disabled.'=>'このタクソノミーのパーマリンクは無効です。','Rewrite the URL using the taxonomy key as the slug. Your permalink structure will be'=>'タクソノミーのキーをスラッグとして使用してURLを書き換えます。パーマリンク構造は次のようになります','Taxonomy Key'=>'タクソノミーキー','Select the type of permalink to use for this taxonomy.'=>'このタクソノミーに使用するパーマリンクのタイプを選択します。','Display a column for the taxonomy on post type listing screens.'=>'投稿タイプの一覧画面にタクソノミーの項目を表示します。','Show Admin Column'=>'管理画面でカラムを表示','Show the taxonomy in the quick/bulk edit panel.'=>'タクソノミーをクイック/一括編集パネルで表示','Quick Edit'=>'クイック編集','List the taxonomy in the Tag Cloud Widget controls.'=>'タグクラウドウィジェットにタクソノミーを表示','Tag Cloud'=>'タグクラウド','A PHP function name to be called for sanitizing taxonomy data saved from a meta box.'=>'メタボックスから保存されたタクソノミーデータをサニタイズするために呼び出されるPHP関数名。','Meta Box Sanitization Callback'=>'メタボックスのサニタイズコールバック','No Meta Box'=>'メタボックスなし','Custom Meta Box'=>'カスタムメタボックス','Meta Box'=>'メタボックス','Tags Meta Box'=>'タグメタボックス','A link to a tag'=>'タグへのリンク','A link to a %s'=>'%s へのリンク','Tag Link'=>'タグリンク','← Go to tags'=>'← タグへ戻る','Back To Items'=>'項目に戻る','← Go to %s'=>'← %s へ戻る','Tags list'=>'タグ一覧','Assigns text to the table hidden heading.'=>'テーブルの非表示見出しにテキストを割り当てます。','Tags list navigation'=>'タグリストナビゲーション','Assigns text to the table pagination hidden heading.'=>'テーブルのページ送りの非表示見出しにテキストを割り当てます。','Filter by category'=>'カテゴリーで絞り込む','Filter By Item'=>'アイテムをフィルタリング','Filter by %s'=>'%s で絞り込む','Description Field Description'=>'フィールドディスクリプションの説明','Assign a parent term to create a hierarchy. The term Jazz, for example, would be the parent of Bebop and Big Band'=>'階層化するには親のタームを指定します。たとえば「ジャズ」というタームを「ビバップ」や「ビッグバンド」の親として指定します。','Describes the Parent field on the Edit Tags screen.'=>'タグの編集画面の親フィールドの説明文です。','Parent Field Description'=>'親フィールドの説明','The "slug" is the URL-friendly version of the name. It is usually all lower case and contains only letters, numbers, and hyphens.'=>'「スラッグ」は、URL に適した形式の名前です。通常はすべて小文字で、文字、数字、ハイフンのみが使用されます。','No tags'=>'タグなし','No Terms'=>'項目はありません','No %s'=>'No %s','No tags found'=>'タグが見つかりませんでした','Not Found'=>'見つかりません','Most Used'=>'最も使われている','Choose From Most Used'=>'よく使うものから選択','Choose from the most used %s'=>'最もよく使われている%sから選択','Add or remove tags'=>'タグの追加もしくは削除','Add Or Remove Items'=>'項目の追加または削除','Add or remove %s'=>'%s を追加または削除する','Separate tags with commas'=>'タグはコンマで区切ってください','Separate Items With Commas'=>'項目が複数ある場合はコンマで区切ってください','Separate %s with commas'=>'%s が複数ある場合はコンマで区切る','Popular Tags'=>'人気のタグ','Popular Items'=>'よく使う項目','Popular %s'=>'人気の %s','Search Tags'=>'タグを検索','Assigns search items text.'=>'検索項目のテキストを割り当てます。','Parent Category:'=>'親カテゴリー:','Parent Category'=>'親カテゴリー','Parent Item'=>'親項目','Parent %s'=>'親 %s','New Tag Name'=>'新規タグ名','New Item Name'=>'新規項目名','New %s Name'=>'新規 %s 名','Add New Tag'=>'新規タグを追加','Update Tag'=>'タグを更新','Update Item'=>'アイテムを更新','Update %s'=>'%s を更新','View Tag'=>'タグを表示','Edit Tag'=>'タグを編集','All Tags'=>'すべての タグ','Assigns the all items text.'=>'すべての項目のテキストを割り当てます。','Assigns the menu name text.'=>'メニュー名のテキストを割り当てます。','Menu Label'=>'メニューラベル','A descriptive summary of the taxonomy.'=>'タクソノミーの説明的要約。','A descriptive summary of the term.'=>'タームの説明的要約。','Term Description'=>'タームの説明','Single word, no spaces. Underscores and dashes allowed.'=>'単語。スペースは不可、アンダースコアとダッシュは使用可能。','Term Slug'=>'タームスラッグ','Term Name'=>'項目名','Default Term'=>'デフォルト項目','Sort Terms'=>'キーワード並び替え','Add Post Type'=>'投稿タイプを追加する','Add Your First Post Type'=>'最初の投稿タイプを追加','Advanced Configuration'=>'高度な設定','Hierarchical'=>'階層的','Public'=>'一般公開','movie'=>'動画','Lower case letters, underscores and dashes only, Max 20 characters.'=>'小文字、アンダースコア、ダッシュのみ、最大20文字。','Movie'=>'映画','Singular Label'=>'単数ラベル','Movies'=>'映画','Plural Label'=>'複数ラベル','Controller Class'=>'コントローラークラス','Namespace Route'=>'名前空間ルート','Base URL'=>'ベース URL','Show In REST API'=>'REST API で表示','Customize the query variable name.'=>'クエリ変数名をカスタマイズ。','Query Variable'=>'クエリー可変','Custom Query Variable'=>'カスタムクエリー変数','Query Variable Support'=>'クエリ変数のサポート','Publicly Queryable'=>'公開クエリ可','Archive Slug'=>'アーカイブスラッグ','Archive'=>'アーカイブ','Pagination'=>'ページ送り','Feed URL'=>'フィード URL','Front URL Prefix'=>'フロント URL プレフィックス','Customize the slug used in the URL.'=>'URL に使用されるスラッグをカスタマイズする。','URL Slug'=>'URL スラッグ','Custom Permalink'=>'カスタムパーマリンク','Post Type Key'=>'投稿タイプキー','Permalink Rewrite'=>'パーマリンクのリライト','Delete With User'=>'ユーザーと一緒に削除','Can Export'=>'エクスポート可能','Plural Capability Name'=>'複数の権限名','Rename Capabilities'=>'リネーム機能','Exclude From Search'=>'検索から除外する','Show In Admin Bar'=>'管理バーの表示','Menu Icon'=>'メニュー アイコン','Menu Position'=>'メニューの位置','Show In UI'=>'UI に表示','A link to a post.'=>'投稿へのリンク。','Item Link Description'=>'リンク項目の説明','A link to a %s.'=>'%s へのリンク。','Post Link'=>'投稿リンク','Item Link'=>'項目のリンク','%s Link'=>'%s リンク','Post updated.'=>'投稿を更新しました。','Item Updated'=>'項目を更新しました','%s updated.'=>'%s を更新しました。','Post scheduled.'=>'投稿を予約しました.','Item Scheduled'=>'公開予約済み項目','%s scheduled.'=>'%s を予約しました。','Post reverted to draft.'=>'投稿を下書きに戻しました。','Item Reverted To Draft'=>'下書きに戻された項目','%s reverted to draft.'=>'%s を下書きに戻しました。','Post published privately.'=>'投稿を限定公開しました。','Item Published Privately'=>'限定公開された項目','Post published.'=>'投稿を公開しました.','Item Published'=>'公開済み項目','%s published.'=>'%s を公開しました。','Posts list'=>'投稿リスト','Items List'=>'項目リスト','%s list'=>'%s リスト','Filter %s by date'=>'%s 日時で絞り込み','Filter posts list'=>'投稿リストの絞り込み','Filter Items List'=>'項目一覧の絞り込み','Filter %s list'=>'%s リストを絞り込み','Uploaded To This Item'=>'この項目にアップロード','Uploaded to this %s'=>'この %s にアップロードされました','Insert into post'=>'投稿に挿入','Insert into %s'=>'%s に挿入','Use Featured Image'=>'アイキャッチ画像を使用','Remove featured image'=>'アイキャッチ画像を削除','Remove Featured Image'=>'アイキャッチ画像を削除','Set featured image'=>'アイキャッチ画像を設定','As the button label when setting the featured image.'=>'アイキャッチ画像を設定する際のボタンラベルとして','Set Featured Image'=>'アイキャッチ画像を設定','Featured image'=>'アイキャッチ画像','Post Attributes'=>'投稿の属性','Attributes Meta Box'=>'属性メタボックス','%s Attributes'=>'%s の属性','Post Archives'=>'投稿アーカイブ','Archives Nav Menu'=>'ナビメニューをアーカイブする','%s Archives'=>'%s アーカイブ','No %s found in Trash'=>'ゴミ箱に%sはありません','No posts found'=>'投稿が見つかりません','No Items Found'=>'項目が見つかりませんでした','No %s found'=>'%s が見つかりませんでした。','Search Posts'=>'投稿を検索','Search Items'=>'項目を検索','Search %s'=>'%s を検索','Parent Page:'=>'親ページ:','Parent %s:'=>'親の%s:','New Post'=>'新規投稿','New Item'=>'新規項目','New %s'=>'新規 %s','Add New Post'=>'新規投稿を追加','Add New Item'=>'新規項目を追加','Add New %s'=>'新規%sを追加','View Posts'=>'投稿一覧を表示','View Items'=>'アイテムを表示','View Post'=>'投稿を表示','View Item'=>'項目を表示','View %s'=>'%s を表示','Edit Post'=>'投稿の編集','Edit Item'=>'項目を編集','Edit %s'=>'%s を編集','All Posts'=>'投稿一覧','All Items'=>'すべての項目','All %s'=>'%s 一覧','Admin menu name for the post type.'=>'投稿タイプの管理メニュー名。','Menu Name'=>'メニュー名','Regenerate'=>'再生成','Add Custom'=>'カスタムの追加','Post Formats'=>'投稿フォーマット','Editor'=>'エディター','Trackbacks'=>'トラックバック','Browse Fields'=>'フィールドを見る','Nothing to import'=>'インポート対象がありません','Imported 1 item'=>'インポートした %s 項目','Export'=>'エクスポート','Select Taxonomies'=>'タクソノミーを選択','Select Post Types'=>'投稿タイプを選択','Category'=>'カテゴリー','Tag'=>'タグ','%s taxonomy created'=>'%s タクソノミーが作成されました','%s taxonomy updated'=>'%s タクソノミーの更新','Taxonomy saved.'=>'タクソノミーを保存する。','Taxonomy deleted.'=>'タクソノミーを削除しました。','Taxonomy updated.'=>'タクソノミーを更新しました。','Taxonomy duplicated.'=>'%s タクソノミーを複製しました。','Taxonomy activated.'=>'%s タクソノミーを有効化しました。','Terms'=>'規約','Post type synchronized.'=>'投稿タイプ %s が同期されました。','Post type duplicated.'=>'投稿タイプ %s が複製されました。','Post type deactivated.'=>'投稿タイプ %s が無効化されました。','Post type activated.'=>'投稿タイプ %s が有効化されました。','Post Types'=>'投稿タイプ','Advanced Settings'=>'高度な設定','Basic Settings'=>'基本設定','This post type could not be registered because its key is in use by another post type registered by another plugin or theme.'=>'この投稿タイプは、このキーが別のプラグインまたはテーマによって登録された別の投稿タイプによって使用されているため、登録できませんでした。','Pages'=>'固定ページ','%s post type created'=>'%s 投稿タイプを作成しました','Add fields to %s'=>'フィールドの追加 %s','%s post type updated'=>'%s 投稿タイプを更新しました','Post type submitted.'=>'投稿タイプを送信しました。','Post type saved.'=>'投稿タイプを保存しました。','Post type updated.'=>'投稿タイプを更新しました。','Post type deleted.'=>'投稿タイプが削除されました。','Type to search...'=>'入力して検索…','PRO Only'=>'PRO 限定','ACF'=>'ACF','taxonomy'=>'タクソノミー','post type'=>'投稿タイプ','Done'=>'完了','Field Group(s)'=>'フィールドグループ','Select one or many field groups...'=>'1つまたは複数のフィールド グループを選択します...','Please select the field groups to link.'=>'リンクするフィールドグループを選択してください。','Field group linked successfully.'=>'フィールドグループが正常にリンクされました。','post statusRegistration Failed'=>'登録に失敗しました','REST API'=>'REST API','Permissions'=>'パーミッション','URLs'=>'URL','Visibility'=>'可視性','Labels'=>'ラベル','Field Settings Tabs'=>'フィールド設定タブ','Close Modal'=>'モーダルを閉じる','Field moved to other group'=>'フィールドは他のグループに移動しました','Close modal'=>'モーダルを閉じる','New Tab Group'=>'新規タブグループ','Save Other Choice'=>'他の選択肢を保存','Allow Other Choice'=>'他の選択肢を許可','Add Toggle All'=>'すべてのトグルを追加','Save Custom Values'=>'カスタム値を保存','Allow Custom Values'=>'カスタム値の許可','Updates'=>'更新','Advanced Custom Fields logo'=>'Advanced Custom Fields ロゴ','Save Changes'=>'変更内容を保存','Field Group Title'=>'フィールドグループタイトル','Add title'=>'タイトルを追加','Add Field Group'=>'フィールドグループを追加する','Add Your First Field Group'=>'最初のフィールドグループを追加','Options Pages'=>'設定ページ','ACF Blocks'=>'ACF Blocks','Gallery Field'=>'ギャラリーフィールド','Flexible Content Field'=>'柔軟コンテンツフィールド','Repeater Field'=>'リピーターフィールド','Delete Field Group'=>'フィールドグループを削除','Group Settings'=>'グループ設定','Location Rules'=>'ロケーションルール','Add Your First Field'=>'最初のフィールドを追加','#'=>'No.','Add Field'=>'フィールドを追加','Presentation'=>'プレゼンテーション','Validation'=>'検証','General'=>'全般','Import JSON'=>'JSON をインポート','Export As JSON'=>'JSON をエクスポート','Deactivate'=>'無効化','Deactivate this item'=>'この項目を無効化する','Activate'=>'有効化','Activate this item'=>'この項目を有効化する','post statusInactive'=>'無効','WP Engine'=>'WP Engine','Advanced Custom Fields and Advanced Custom Fields PRO should not be active at the same time. We\'ve automatically deactivated Advanced Custom Fields PRO.'=>'Advanced Custom Fields と Advanced Custom Fields PRO を同時に有効化しないでください。
Advanced Custom Fields PROを自動的に無効化しました。','Advanced Custom Fields and Advanced Custom Fields PRO should not be active at the same time. We\'ve automatically deactivated Advanced Custom Fields.'=>'Advanced Custom Fields と Advanced Custom Fields PRO を同時に有効化しないでください。
Advanced Custom Fields を自動的に無効化しました。','%1$s must have a valid user ID.'=>'%1$s は有効なユーザー ID である必要があります。','Invalid request.'=>'無効なリクエストです。','%1$s is not one of %2$s'=>'%1$s は %2$s に当てはまりません','%1$s must have term %2$s.'=>'%1$s はターム %2$s である必要があります。','%1$s must be of post type %2$s.'=>'%1$s は投稿タイプ %2$s である必要があります。','%1$s must have a valid post ID.'=>'%1$s は有効な投稿 ID である必要があります。','%s requires a valid attachment ID.'=>'%s には有効な添付ファイル ID が必要です。','Show in REST API'=>'REST API で表示','Enable Transparency'=>'透明度の有効化','RGBA Array'=>'RGBA 配列','RGBA String'=>'RGBA 文字列','Hex String'=>'16進値文字列','Upgrade to PRO'=>'プロ版にアップグレード','post statusActive'=>'有効','\'%s\' is not a valid email address'=>'\'%s\' は有効なメールアドレスではありません','Color value'=>'明度','Select default color'=>'デフォルトの色を選択','Clear color'=>'色をクリア','Blocks'=>'ブロック','Options'=>'オプション','Users'=>'ユーザー','Menu items'=>'メニュー項目','Widgets'=>'ウィジェット','Attachments'=>'添付ファイル','Taxonomies'=>'タクソノミー','Posts'=>'投稿','Last updated: %s'=>'最終更新日: %s','Sorry, this post is unavailable for diff comparison.'=>'このフィールドグループは diff 比較に使用できません。','Invalid field group parameter(s).'=>'無効なフィールドグループパラメータ。','Awaiting save'=>'保存待ち','Saved'=>'保存しました','Import'=>'インポート','Review changes'=>'変更をレビュー','Located in: %s'=>'位置: %s','Located in plugin: %s'=>'プラグイン中の位置: %s','Located in theme: %s'=>'テーマ内の位置: %s','Various'=>'各種','Sync changes'=>'変更を同期','Loading diff'=>'差分を読み込み中','Review local JSON changes'=>'ローカルの JSON 変更をレビュー','Visit website'=>'サイトへ移動','View details'=>'詳細を表示','Version %s'=>'バージョン %s','Information'=>'情報','<a href="%s" target="_blank">Help Desk</a>. The support professionals on our Help Desk will assist with your more in depth, technical challenges.'=>'<a href="%s" target="_blank">ヘルプデスク</a>。サポートの専門家がお客様のより詳細な技術的課題をサポートします。','<a href="%s" target="_blank">Discussions</a>. We have an active and friendly community on our Community Forums who may be able to help you figure out the \'how-tos\' of the ACF world.'=>'<a href="%s" target="_blank">ディスカッション</a>。コミュニティフォーラムには、活発でフレンドリーなコミュニティがあり、ACFの世界の「ハウツー」を理解する手助けをしてくれるかもしれません。','<a href="%s" target="_blank">Documentation</a>. Our extensive documentation contains references and guides for most situations you may encounter.'=>'<a href="%s" target="_blank">ドキュメンテーション</a>。私たちの豊富なドキュメントには、お客様が遭遇する可能性のあるほとんどの状況に対するリファレンスやガイドが含まれています。','We are fanatical about support, and want you to get the best out of your website with ACF. If you run into any difficulties, there are several places you can find help:'=>'私たちはサポートを非常に重要視しており、ACF を使ったサイトを最大限に活用していただきたいと考えています。何か問題が発生した場合には、複数の場所でサポートを受けることができます:','Help & Support'=>'ヘルプとサポート','Please use the Help & Support tab to get in touch should you find yourself requiring assistance.'=>'お困りの際は「ヘルプとサポート」タブからお問い合わせください。','Before creating your first Field Group, we recommend first reading our <a href="%s" target="_blank">Getting started</a> guide to familiarize yourself with the plugin\'s philosophy and best practises.'=>'初めてフィールドグループを作成する前にまず<a href="%s" target="_blank">スタートガイド</a>に目を通して、プラグインの理念やベストプラクティスを理解することをおすすめします。','The Advanced Custom Fields plugin provides a visual form builder to customize WordPress edit screens with extra fields, and an intuitive API to display custom field values in any theme template file.'=>'Advanced Custom Fields プラグインは、WordPress の編集画面を追加フィールドでカスタマイズするためのビジュアルフォームビルダーと、カスタムフィールドの値を任意のテーマテンプレートファイルに表示するための直感的な API を提供します。','Overview'=>'概要','Location type "%s" is already registered.'=>'位置タイプ「%s」はすでに登録されています。','Class "%s" does not exist.'=>'クラス "%s" は存在しません。','Invalid nonce.'=>'無効な nonce。','Error loading field.'=>'フィールドの読み込みエラー。','<strong>Error</strong>: %s'=>'<strong>エラー</strong>: %s','Widget'=>'ウィジェット','User Role'=>'ユーザー権限グループ','Comment'=>'コメント','Post Format'=>'投稿フォーマット','Menu Item'=>'メニュー項目','Post Status'=>'投稿ステータス','Menus'=>'メニュー','Menu Locations'=>'メニューの位置','Menu'=>'メニュー','Post Taxonomy'=>'投稿タクソノミー','Child Page (has parent)'=>'子ページ (親ページあり)','Parent Page (has children)'=>'親ページ (子ページあり)','Top Level Page (no parent)'=>'最上位レベルのページ (親ページなし)','Posts Page'=>'投稿ページ','Front Page'=>'フロントページ','Page Type'=>'ページタイプ','Viewing back end'=>'バックエンドで表示','Viewing front end'=>'フロントエンドで表示','Logged in'=>'ログイン済み','Current User'=>'現在のユーザー','Page Template'=>'固定ページテンプレート','Register'=>'登録','Add / Edit'=>'追加 / 編集','User Form'=>'ユーザーフォーム','Page Parent'=>'親ページ','Super Admin'=>'特権管理者','Current User Role'=>'現在のユーザー権限グループ','Default Template'=>'デフォルトテンプレート','Post Template'=>'投稿テンプレート','Post Category'=>'投稿カテゴリー','All %s formats'=>'すべての%sフォーマット','Attachment'=>'添付ファイル','%s value is required'=>'%s の値は必須です','Show this field if'=>'このフィールドグループの表示条件','Conditional Logic'=>'条件判定','and'=>'と','Local JSON'=>'ローカル JSON','Clone Field'=>'フィールドを複製','Please also check all premium add-ons (%s) are updated to the latest version.'=>'また、すべてのプレミアムアドオン ( %s) が最新版に更新されていることを確認してください。','This version contains improvements to your database and requires an upgrade.'=>'このバージョンにはデータベースの改善が含まれており、アップグレードが必要です。','Thank you for updating to %1$s v%2$s!'=>'%1$s v%2$sへの更新をありがとうございます。','Database Upgrade Required'=>'データベースのアップグレードが必要','Options Page'=>'オプションページ','Gallery'=>'ギャラリー','Flexible Content'=>'柔軟なコンテンツ','Repeater'=>'繰り返し','Back to all tools'=>'すべてのツールに戻る','If multiple field groups appear on an edit screen, the first field group\'s options will be used (the one with the lowest order number)'=>'複数のフィールドグループが編集画面に表示される場合、最初のフィールドグループ (最小の番号を持つもの) のオプションが使用されます','<b>Select</b> items to <b>hide</b> them from the edit screen.'=>'編集画面で<b>非表示</b>にする項目を<b>選択して</b>ください。','Hide on screen'=>'画面上で非表示','Send Trackbacks'=>'トラックバック送信','Tags'=>'タグ','Categories'=>'カテゴリー','Page Attributes'=>'ページ属性','Format'=>'フォーマット','Author'=>'投稿者','Slug'=>'スラッグ','Revisions'=>'リビジョン','Comments'=>'コメント','Discussion'=>'ディスカッション','Excerpt'=>'抜粋','Content Editor'=>'コンテンツエディター','Permalink'=>'パーマリンク','Shown in field group list'=>'フィールドグループリストに表示','Field groups with a lower order will appear first'=>'下位のフィールドグループを最初に表示','Order No.'=>'注文番号','Below fields'=>'フィールドの下','Below labels'=>'ラベルの下','Instruction Placement'=>'手順の配置','Label Placement'=>'ラベルの配置','Side'=>'サイド','Normal (after content)'=>'通常 (コンテンツの後)','High (after title)'=>'高 (タイトルの後)','Position'=>'位置','Seamless (no metabox)'=>'シームレス (メタボックスなし)','Standard (WP metabox)'=>'標準 (WP メタボックス)','Style'=>'スタイル','Type'=>'タイプ','Key'=>'キー','Order'=>'順序','Close Field'=>'フィールドを閉じる','id'=>'ID','class'=>'クラス','width'=>'横幅','Wrapper Attributes'=>'ラッパー属性','Required'=>'必須項目','Instructions'=>'手順','Field Type'=>'フィールドタイプ','Single word, no spaces. Underscores and dashes allowed'=>'スペースは不可、アンダースコアとダッシュは使用可能','Field Name'=>'フィールド名','This is the name which will appear on the EDIT page'=>'これは、編集ページに表示される名前です','Field Label'=>'フィールドラベル','Delete'=>'削除','Delete field'=>'フィールドを削除','Move'=>'移動','Move field to another group'=>'フィールドを別のグループへ移動','Duplicate field'=>'フィールドを複製','Edit field'=>'フィールドを編集','Drag to reorder'=>'ドラッグして順序を変更','Show this field group if'=>'このフィールドグループを表示する条件','No updates available.'=>'利用可能な更新はありません。','Database upgrade complete. <a href="%s">See what\'s new</a>'=>'データベースのアップグレードが完了しました。<a href="%s">変更点を表示</a>','Reading upgrade tasks...'=>'アップグレードタスクを読み込んでいます...','Upgrade failed.'=>'アップグレードに失敗しました。','Upgrade complete.'=>'アップグレードが完了しました。','Upgrading data to version %s'=>'データをバージョン%sへアップグレード中','It is strongly recommended that you backup your database before proceeding. Are you sure you wish to run the updater now?'=>'続行する前にデータベースをバックアップすることを強くおすすめします。本当に更新ツールを今すぐ実行してもよいですか ?','Please select at least one site to upgrade.'=>'アップグレードするサイトを1つ以上選択してください。','Database Upgrade complete. <a href="%s">Return to network dashboard</a>'=>'データベースのアップグレードが完了しました。<a href="%s">ネットワークダッシュボードに戻る</a>','Site is up to date'=>'サイトは最新状態です','Site requires database upgrade from %1$s to %2$s'=>'%1$s から %2$s へのデータベースのアップグレードが必要です','Site'=>'サイト','Upgrade Sites'=>'サイトをアップグレード','The following sites require a DB upgrade. Check the ones you want to update and then click %s.'=>'以下のサイトはデータベースのアップグレードが必要です。更新したいものにチェックを入れて、%s をクリックしてください。','Add rule group'=>'ルールグループを追加','Create a set of rules to determine which edit screens will use these advanced custom fields'=>'どの編集画面でカスタムフィールドを表示するかを決定するルールを作成します','Rules'=>'ルール','Copied'=>'コピーしました','Copy to clipboard'=>'クリップボードにコピー','Select the items you would like to export and then select your export method. Export As JSON to export to a .json file which you can then import to another ACF installation. Generate PHP to export to PHP code which you can place in your theme.'=>'エクスポートしたい項目とエクスポート方法を選んでください。「JSON としてエクスポート」では別の ACF をインストールした環境でインポートできる JSON ファイルがエクスポートされます。「PHP の生成」ではテーマ内で利用できる PHP コードが生成されます。','Select Field Groups'=>'フィールドグループを選択','No field groups selected'=>'フィールド未選択','Generate PHP'=>'PHP を生成','Export Field Groups'=>'フィールドグループをエクスポート','Import file empty'=>'空ファイルのインポート','Incorrect file type'=>'不正なファイルの種類','Error uploading file. Please try again'=>'ファイルアップロードエラー。もう一度お試しください','Select the Advanced Custom Fields JSON file you would like to import. When you click the import button below, ACF will import the items in that file.'=>'インポートしたい ACF の JSON ファイルを選択してください。下のインポートボタンをクリックすると、ACF はファイルに項目をインポートします。','Import Field Groups'=>'フィールドグループをインポート','Sync'=>'同期','Select %s'=>'%sを選択','Duplicate'=>'複製','Duplicate this item'=>'この項目を複製','Supports'=>'サポート','Documentation'=>'ドキュメンテーション','Description'=>'説明','Sync available'=>'同期が利用できます','Field group synchronized.'=>'%s件のフィールドグループを同期しました。','Field group duplicated.'=>'%s件のフィールドグループを複製しました。','Active <span class="count">(%s)</span>'=>'使用中 <span class="count">(%s)</span>','Review sites & upgrade'=>'サイトをレビューしてアップグレード','Upgrade Database'=>'データベースをアップグレード','Custom Fields'=>'カスタムフィールド','Move Field'=>'フィールドを移動','Please select the destination for this field'=>'このフィールドの移動先を選択してください','The %1$s field can now be found in the %2$s field group'=>'%1$s フィールドは現在 %2$s フィールドグループにあります','Move Complete.'=>'移動が完了しました。','Active'=>'有効','Field Keys'=>'フィールドキー','Settings'=>'設定','Location'=>'位置','Null'=>'Null','copy'=>'コピー','(this field)'=>'(このフィールド)','Checked'=>'チェック済み','Move Custom Field'=>'カスタムフィールドを移動','No toggle fields available'=>'利用可能な切り替えフィールドがありません','Field group title is required'=>'フィールドグループのタイトルは必須です','This field cannot be moved until its changes have been saved'=>'変更を保存するまでこのフィールドは移動できません','The string "field_" may not be used at the start of a field name'=>'"field_" という文字列はフィールド名の先頭に使うことはできません','Field group draft updated.'=>'フィールドグループ下書きを更新しました。','Field group scheduled for.'=>'フィールドグループを公開予約しました。','Field group submitted.'=>'フィールドグループを送信しました。','Field group saved.'=>'フィールドグループを保存しました。','Field group published.'=>'フィールドグループを公開しました。','Field group deleted.'=>'フィールドグループを削除しました。','Field group updated.'=>'フィールドグループを更新しました。','Tools'=>'ツール','is not equal to'=>'等しくない','is equal to'=>'等しい','Forms'=>'フォーム','Page'=>'固定ページ','Post'=>'投稿','Relational'=>'関連','Choice'=>'選択','Basic'=>'基本','Unknown'=>'不明','Field type does not exist'=>'フィールドタイプが存在しません','Spam Detected'=>'スパムを検出しました','Post updated'=>'投稿を更新しました','Update'=>'更新','Validate Email'=>'メールを確認','Content'=>'コンテンツ','Title'=>'タイトル','Edit field group'=>'フィールドグループを編集','Selection is less than'=>'選択範囲が以下より小さい場合','Selection is greater than'=>'選択範囲が以下より大きい場合','Value is less than'=>'値が以下より小さい場合','Value is greater than'=>'値が以下より大きい場合','Value contains'=>'以下の値が含まれる場合','Value matches pattern'=>'値が以下のパターンに一致する場合','Value is not equal to'=>'値が以下に等しくない場合','Value is equal to'=>'値が以下に等しい場合','Has no value'=>'値がない場合','Has any value'=>'任意の値あり','Cancel'=>'キャンセル','Are you sure?'=>'本当に実行しますか ?','%d fields require attention'=>'%d個のフィールドで確認が必要です','1 field requires attention'=>'1つのフィールドで確認が必要です','Validation failed'=>'検証失敗','Validation successful'=>'検証成功','Restricted'=>'制限','Collapse Details'=>'詳細を折りたたむ','Expand Details'=>'詳細を展開','Uploaded to this post'=>'この投稿へのアップロード','verbUpdate'=>'更新','verbEdit'=>'編集','The changes you made will be lost if you navigate away from this page'=>'このページから移動した場合、変更は失われます','File type must be %s.'=>'ファイル形式は %s である必要があります。','or'=>'または','File size must not exceed %s.'=>'ファイルサイズは %s 以下である必要があります。','File size must be at least %s.'=>'ファイルサイズは %s 以上である必要があります。','Image height must not exceed %dpx.'=>'画像の高さは %dpx 以下である必要があります。','Image height must be at least %dpx.'=>'画像の高さは %dpx 以上である必要があります。','Image width must not exceed %dpx.'=>'画像の幅は %dpx 以下である必要があります。','Image width must be at least %dpx.'=>'画像の幅は %dpx 以上である必要があります。','(no title)'=>'(タイトルなし)','Full Size'=>'フルサイズ','Large'=>'大','Medium'=>'中','Thumbnail'=>'サムネイル','(no label)'=>'(ラベルなし)','Sets the textarea height'=>'テキストエリアの高さを設定','Rows'=>'行','Text Area'=>'テキストエリア','Prepend an extra checkbox to toggle all choices'=>'追加のチェックボックスを先頭に追加して、すべての選択肢を切り替えます','Save \'custom\' values to the field\'s choices'=>'フィールドの選択肢として「カスタム」を保存する','Allow \'custom\' values to be added'=>'「カスタム」値の追加を許可する','Add new choice'=>'新規選択肢を追加','Toggle All'=>'すべて切り替え','Allow Archives URLs'=>'アーカイブ URL を許可','Archives'=>'アーカイブ','Page Link'=>'ページリンク','Add'=>'追加','Name'=>'名前','%s added'=>'%s を追加しました','%s already exists'=>'%s はすでに存在しています','User unable to add new %s'=>'ユーザーが新規 %s を追加できません','Term ID'=>'ターム ID','Term Object'=>'タームオブジェクト','Load value from posts terms'=>'投稿タームから値を読み込む','Load Terms'=>'タームを読み込む','Connect selected terms to the post'=>'選択したタームを投稿に関連付ける','Save Terms'=>'タームを保存','Allow new terms to be created whilst editing'=>'編集中に新しいタームを作成できるようにする','Create Terms'=>'タームを追加','Radio Buttons'=>'ラジオボタン','Single Value'=>'単一値','Multi Select'=>'複数選択','Checkbox'=>'チェックボックス','Multiple Values'=>'複数値','Select the appearance of this field'=>'このフィールドの外観を選択','Appearance'=>'外観','Select the taxonomy to be displayed'=>'表示するタクソノミーを選択','No TermsNo %s'=>'%s なし','Value must be equal to or lower than %d'=>'値は%d文字以下である必要があります','Value must be equal to or higher than %d'=>'値は%d文字以上である必要があります','Value must be a number'=>'値は数字である必要があります','Number'=>'番号','Save \'other\' values to the field\'s choices'=>'フィールドの選択肢として「その他」を保存する','Add \'other\' choice to allow for custom values'=>'「その他」の選択肢を追加してカスタム値を許可','Other'=>'その他','Radio Button'=>'ラジオボタン','Define an endpoint for the previous accordion to stop. This accordion will not be visible.'=>'前のアコーディオンを停止するエンドポイントを定義します。このアコーディオンは表示されません。','Allow this accordion to open without closing others.'=>'他のアコーディオンを閉じずにこのアコーディオンを開くことができるようにする。','Multi-Expand'=>'マルチ展開','Display this accordion as open on page load.'=>'このアコーディオンをページの読み込み時に開いた状態で表示します。','Open'=>'受付中','Accordion'=>'アコーディオン','Restrict which files can be uploaded'=>'アップロード可能なファイルを制限','File ID'=>'ファイル ID','File URL'=>'ファイルの URL','File Array'=>'ファイル配列','Add File'=>'ファイルを追加','No file selected'=>'ファイルが選択されていません','File name'=>'ファイル名','Update File'=>'ファイルを更新','Edit File'=>'ファイルを編集','Select File'=>'ファイルを選択','File'=>'ファイル','Password'=>'パスワード','Specify the value returned'=>'戻り値を指定します','Use AJAX to lazy load choices?'=>'AJAX を使用して選択肢を遅延読み込みしますか ?','Enter each default value on a new line'=>'新しい行に各デフォルト値を入力してください','verbSelect'=>'選択','Select2 JS load_failLoading failed'=>'読み込み失敗','Select2 JS searchingSearching&hellip;'=>'検索中&hellip;','Select2 JS load_moreLoading more results&hellip;'=>'結果をさらに読み込み中&hellip;','Select2 JS selection_too_long_nYou can only select %d items'=>'%d項目のみ選択可能です','Select2 JS selection_too_long_1You can only select 1 item'=>'1項目のみ選択可能です','Select2 JS input_too_long_nPlease delete %d characters'=>'%d文字を削除してください','Select2 JS input_too_long_1Please delete 1 character'=>'1文字削除してください','Select2 JS input_too_short_nPlease enter %d or more characters'=>'%d文字以上を入力してください','Select2 JS input_too_short_1Please enter 1 or more characters'=>'1つ以上の文字を入力してください','Select2 JS matches_0No matches found'=>'一致する項目がありません','Select2 JS matches_n%d results are available, use up and down arrow keys to navigate.'=>'%d件の結果が見つかりました。上下矢印キーを使って移動してください。','Select2 JS matches_1One result is available, press enter to select it.'=>'1件の結果が利用可能です。Enter を押して選択してください。','nounSelect'=>'選択','User ID'=>'ユーザー ID','User Object'=>'ユーザーオブジェクト','User Array'=>'ユーザー配列','All user roles'=>'すべてのユーザー権限グループ','Filter by Role'=>'権限グループで絞り込む','User'=>'ユーザー','Separator'=>'区切り','Select Color'=>'色を選択','Default'=>'デフォルト','Clear'=>'クリア','Color Picker'=>'カラーピッカー','Date Time Picker JS pmTextShortP'=>'P','Date Time Picker JS pmTextPM'=>'PM','Date Time Picker JS amTextShortA'=>'A','Date Time Picker JS amTextAM'=>'AM','Date Time Picker JS selectTextSelect'=>'選択','Date Time Picker JS closeTextDone'=>'完了','Date Time Picker JS currentTextNow'=>'現在','Date Time Picker JS timezoneTextTime Zone'=>'タイムゾーン','Date Time Picker JS microsecTextMicrosecond'=>'マイクロ秒','Date Time Picker JS millisecTextMillisecond'=>'ミリ秒','Date Time Picker JS secondTextSecond'=>'秒','Date Time Picker JS minuteTextMinute'=>'分','Date Time Picker JS hourTextHour'=>'時','Date Time Picker JS timeTextTime'=>'時間','Date Time Picker JS timeOnlyTitleChoose Time'=>'時間を選択','Date Time Picker'=>'日時選択ツール','Endpoint'=>'エンドポイント','Left aligned'=>'左揃え','Top aligned'=>'上揃え','Placement'=>'配置','Tab'=>'タブ','Value must be a valid URL'=>'値は有効な URL である必要があります','Link URL'=>'リンク URL','Link Array'=>'リンク配列','Opens in a new window/tab'=>'新しいウィンドウまたはタブで開く','Select Link'=>'リンクを選択','Link'=>'リンク','Email'=>'メール','Step Size'=>'ステップサイズ','Maximum Value'=>'最大値','Minimum Value'=>'最小値','Range'=>'範囲','Both (Array)'=>'両方 (配列)','Label'=>'ラベル','Value'=>'値','Vertical'=>'垂直','Horizontal'=>'水平','red : Red'=>'red : Red','For more control, you may specify both a value and label like this:'=>'下記のように記述すると、値とラベルの両方を制御することができます:','Enter each choice on a new line.'=>'選択肢を改行で区切って入力してください。','Choices'=>'選択肢','Button Group'=>'ボタングループ','Allow Null'=>'空の値を許可','Parent'=>'親','TinyMCE will not be initialized until field is clicked'=>'フィールドがクリックされるまで TinyMCE は初期化されません','Delay Initialization'=>'初期化を遅延させる','Show Media Upload Buttons'=>'メディアアップロードボタンを表示','Toolbar'=>'ツールバー','Text Only'=>'テキストのみ','Visual Only'=>'ビジュアルのみ','Visual & Text'=>'ビジュアルとテキスト','Tabs'=>'タブ','Click to initialize TinyMCE'=>'クリックして TinyMCE を初期化','Name for the Text editor tab (formerly HTML)Text'=>'テキスト','Visual'=>'ビジュアル','Value must not exceed %d characters'=>'値は%d文字以内である必要があります','Leave blank for no limit'=>'制限しない場合は空白にする','Character Limit'=>'文字数制限','Appears after the input'=>'入力内容の後に表示','Append'=>'追加','Appears before the input'=>'入力内容の前に表示','Prepend'=>'先頭に追加','Appears within the input'=>'入力内容の中に表示','Placeholder Text'=>'プレースホルダーテキスト','Appears when creating a new post'=>'新規投稿作成時に表示','Text'=>'テキスト','%1$s requires at least %2$s selection'=>'%1$sは%2$s個以上選択する必要があります','Post ID'=>'投稿 ID','Post Object'=>'投稿オブジェクト','Maximum Posts'=>'最大投稿数','Minimum Posts'=>'最小投稿数','Featured Image'=>'アイキャッチ画像','Selected elements will be displayed in each result'=>'選択した要素がそれぞれの結果に表示されます','Elements'=>'要素','Taxonomy'=>'タクソノミー','Post Type'=>'投稿タイプ','Filters'=>'フィルター','All taxonomies'=>'すべてのタクソノミー','Filter by Taxonomy'=>'タクソノミーで絞り込み','All post types'=>'すべての投稿タイプ','Filter by Post Type'=>'投稿タイプでフィルター','Search...'=>'検索…','Select taxonomy'=>'タクソノミーを選択','Select post type'=>'投稿タイプを選択','No matches found'=>'一致する項目がありません','Loading'=>'読み込み中','Maximum values reached ( {max} values )'=>'最大値 ({max}) に達しました','Relationship'=>'関係','Comma separated list. Leave blank for all types'=>'カンマ区切りのリスト。すべてのタイプを許可する場合は空白のままにします','Allowed File Types'=>'許可されるファイルの種類','Maximum'=>'最大','File size'=>'ファイルサイズ','Restrict which images can be uploaded'=>'アップロード可能な画像を制限','Minimum'=>'最小','Uploaded to post'=>'投稿にアップロード','All'=>'すべて','Limit the media library choice'=>'メディアライブラリの選択肢を制限','Library'=>'ライブラリ','Preview Size'=>'プレビューサイズ','Image ID'=>'画像 ID','Image URL'=>'画像 URL','Image Array'=>'画像配列','Specify the returned value on front end'=>'フロントエンドへの返り値を指定してください','Return Value'=>'返り値','Add Image'=>'画像を追加','No image selected'=>'画像が選択されていません','Remove'=>'削除','Edit'=>'編集','All images'=>'すべての画像','Update Image'=>'画像を更新','Edit Image'=>'画像を編集','Select Image'=>'画像を選択','Image'=>'画像','Allow HTML markup to display as visible text instead of rendering'=>'HTML マークアップのコードとして表示を許可','Escape HTML'=>'HTML をエスケープ','No Formatting'=>'書式設定なし','Automatically add &lt;br&gt;'=>'自動的に &lt;br&gt; を追加','Automatically add paragraphs'=>'自動的に段落追加する','Controls how new lines are rendered'=>'改行をどのように表示するか制御','New Lines'=>'改行','Week Starts On'=>'週の始まり','The format used when saving a value'=>'値を保存するときに使用される形式','Save Format'=>'書式を保存','Date Picker JS weekHeaderWk'=>'Wk','Date Picker JS prevTextPrev'=>'前へ','Date Picker JS nextTextNext'=>'次へ','Date Picker JS currentTextToday'=>'今日','Date Picker JS closeTextDone'=>'完了','Date Picker'=>'日付選択ツール','Width'=>'幅','Embed Size'=>'埋め込みサイズ','Enter URL'=>'URL を入力','oEmbed'=>'oEmbed','Text shown when inactive'=>'無効化時に表示されるテキスト','Off Text'=>'無効化時のテキスト','Text shown when active'=>'有効時に表示するテキスト','On Text'=>'アクティブ時のテキスト','Stylized UI'=>'スタイリッシュな UI','Default Value'=>'初期値','Displays text alongside the checkbox'=>'チェックボックスの横にテキストを表示','Message'=>'メッセージ','No'=>'いいえ','Yes'=>'はい','True / False'=>'真/偽','Row'=>'行','Table'=>'テーブル','Block'=>'ブロック','Specify the style used to render the selected fields'=>'選択したフィールドのレンダリングに使用されるスタイルを指定します','Layout'=>'レイアウト','Sub Fields'=>'サブフィールド','Group'=>'グループ','Customize the map height'=>'地図の高さをカスタマイズ','Height'=>'高さ','Set the initial zoom level'=>'地図のデフォルトズームレベルを設定','Zoom'=>'ズーム','Center the initial map'=>'地図のデフォルト中心位置を設定','Center'=>'中央','Search for address...'=>'住所を検索…','Find current location'=>'現在の場所を検索','Clear location'=>'位置情報をクリア','Search'=>'検索','Sorry, this browser does not support geolocation'=>'お使いのブラウザーは位置情報機能に対応していません','Google Map'=>'Google マップ','The format returned via template functions'=>'テンプレート関数で返されるフォーマット','Return Format'=>'戻り値の形式','Custom:'=>'カスタム:','The format displayed when editing a post'=>'投稿編集時に表示される書式','Display Format'=>'表示形式','Time Picker'=>'時間選択ツール','Inactive <span class="count">(%s)</span>'=>'停止中 <span class="count">(%s)</span>','No Fields found in Trash'=>'ゴミ箱にフィールドが見つかりません','No Fields found'=>'フィールドが見つかりません','Search Fields'=>'フィールドを検索','View Field'=>'フィールドを表示','New Field'=>'新規フィールド','Edit Field'=>'フィールドを編集','Add New Field'=>'新規フィールドを追加','Field'=>'フィールド','Fields'=>'フィールド','No Field Groups found in Trash'=>'ゴミ箱にフィールドグループが見つかりません','No Field Groups found'=>'フィールドグループが見つかりません','Search Field Groups'=>'フィールドグループを検索','View Field Group'=>'フィールドグループを表示','New Field Group'=>'新規フィールドグループ','Edit Field Group'=>'フィールドグループを編集','Add New Field Group'=>'新規フィールドグループを追加','Add New'=>'新規追加','Field Group'=>'フィールドグループ','Field Groups'=>'フィールドグループ','Customize WordPress with powerful, professional and intuitive fields.'=>'パワフル、プロフェッショナル、直感的なフィールドで WordPress をカスタマイズ。','https://www.advancedcustomfields.com'=>'https://www.advancedcustomfields.com','Advanced Custom Fields'=>'Advanced Custom Fields','Advanced Custom Fields PRO'=>'Advanced Custom Fields PRO','Options Updated'=>'オプションを更新しました','Check Again'=>'再確認','Publish'=>'公開','No Custom Field Groups found for this options page. <a href="%s">Create a Custom Field Group</a>'=>'このオプションページにカスタムフィールドグループがありません. <a href="%s">カスタムフィールドグループを作成</a>','<b>Error</b>. Could not connect to update server'=>'<b>エラー</b> 更新サーバーに接続できません','Display'=>'表示','Add Row'=>'行を追加','layout'=>'レイアウト','layouts'=>'レイアウト','This field requires at least {min} {label} {identifier}'=>'{identifier}に{label}は最低{min}個必要です','{available} {label} {identifier} available (max {max})'=>'あと{available}個 {identifier}には {label} を利用できます（最大 {max}個）','{required} {label} {identifier} required (min {min})'=>'あと{required}個 {identifier}には {label} を利用する必要があります（最小 {max}個）','Flexible Content requires at least 1 layout'=>'柔軟コンテンツは少なくとも1個のレイアウトが必要です','Click the "%s" button below to start creating your layout'=>'下の "%s" ボタンをクリックしてレイアウトの作成を始めてください','Add layout'=>'レイアウトを追加','Remove layout'=>'レイアウトを削除','Delete Layout'=>'レイアウトを削除','Duplicate Layout'=>'レイアウトを複製','Add New Layout'=>'新しいレイアウトを追加','Add Layout'=>'レイアウトを追加','Min'=>'最小数','Max'=>'最大数','Minimum Layouts'=>'レイアウトの最小数','Maximum Layouts'=>'レイアウトの最大数','Button Label'=>'ボタンのラベル','Add Image to Gallery'=>'ギャラリーに画像を追加','Maximum selection reached'=>'選択の最大数に到達しました','Length'=>'長さ','Add to gallery'=>'ギャラリーを追加','Bulk actions'=>'一括操作','Sort by date uploaded'=>'アップロード日で並べ替え','Sort by date modified'=>'変更日で並び替え','Sort by title'=>'タイトルで並び替え','Reverse current order'=>'並び順を逆にする','Close'=>'閉じる','Minimum Selection'=>'最小選択数','Maximum Selection'=>'最大選択数','Allowed file types'=>'許可するファイルタイプ','Minimum rows not reached ({min} rows)'=>'最小行数に達しました（{min} 行）','Maximum rows reached ({max} rows)'=>'最大行数に達しました（{max} 行）','Minimum Rows'=>'最小行数','Maximum Rows'=>'最大行数','Click to reorder'=>'ドラッグして並び替え','Add row'=>'行を追加','Remove row'=>'行を削除','First Page'=>'フロントページ','Previous Page'=>'投稿ページ','Next Page'=>'フロントページ','Last Page'=>'投稿ページ','No options pages exist'=>'オプションページはありません','Deactivate License'=>'ライセンスのアクティベートを解除','Activate License'=>'ライセンスをアクティベート','License Key'=>'ライセンスキー','Update Information'=>'アップデート情報','Current Version'=>'現在のバージョン','Latest Version'=>'最新のバージョン','Update Available'=>'利用可能なアップデート','Upgrade Notice'=>'アップグレード通知','Enter your license key to unlock updates'=>'アップデートのロックを解除するためにライセンスキーを入力してください','Update Plugin'=>'プラグインをアップデート']];
<?php
return ['domain'=>NULL,'plural-forms'=>NULL,'language'=>'ru_RU','project-id-version'=>'Advanced Custom Fields','pot-creation-date'=>'2025-05-08T10:32:33+00:00','po-revision-date'=>'2025-05-08T10:20:52+00:00','x-generator'=>'gettext','messages'=>['Number of Field Groups with Blocks and Other Locations'=>'Количество групп полей с правилами размещения как для блоков, так и для других типов.','Number of Field Groups with Multiple Block Locations'=>'Количество групп полей, у которых несколько правил размещения для блоков.','Number of Field Groups with a Single Block Location'=>'Количество групп полей с единственным правилом размещения типа "Блок".','All Location Rules'=>'Все правила местонахождения','Learn more'=>'Подробнее','ACF was unable to perform validation because the provided nonce failed verification.'=>'ACF не смог выполнить проверку, потому что переданный nonce не прошёл верификацию.','ACF was unable to perform validation because no nonce was received by the server.'=>'ACF не смог выполнить проверку, потому что сервер не получил nonce.','are developed and maintained by'=>'разработано и обслуживается','By default only admin users can edit this setting.'=>'По умолчанию этот параметр могут редактировать только администраторы.','By default only super admin users can edit this setting.'=>'По умолчанию этот параметр могут редактировать только суперадминистраторы.','Close and Add Field'=>'Закрыть и добавить поле','A PHP function name to be called to handle the content of a meta box on your taxonomy. For security, this callback will be executed in a special context without access to any superglobals like $_POST or $_GET.'=>'Имя PHP функции, которая будет вызвана для обработки содержимого мета-блока в вашей таксономии. Для безопасности этот обратный вызов будет выполняться в специальном контексте без доступа к каким-либо суперглобальным переменным, таким как $_POST или $_GET.','A PHP function name to be called when setting up the meta boxes for the edit screen. For security, this callback will be executed in a special context without access to any superglobals like $_POST or $_GET.'=>'Имя PHP-функции, которая будет вызвана при настройке мета-боксов для экрана редактирования. Для безопасности этот обратный вызов будет выполняться в специальном контексте без доступа к каким-либо суперглобальным переменным, таким как $_POST или $_GET.','Allow Access to Value in Editor UI'=>'Разрешить доступ к значению в пользовательском интерфейсе редактора','Learn more.'=>'Узнать больше.','Allow content editors to access and display the field value in the editor UI using Block Bindings or the ACF Shortcode. %s'=>'Откройте доступ редакторам содержимого к отображению значения поля в интерфейсе редактора с помощью привязки к блоку или шорткода ACF. %s','The requested ACF field is not allowed to be output in bindings or the ACF Shortcode.'=>'Запрашиваемое поле ACF не разрешено для вывода в связке блока или шорткоде ACF.','The requested ACF field type does not support output in bindings or the ACF Shortcode.'=>'Запрашиваемый тип поля ACF не поддерживает вывод в связках или шорткоде ACF.','[The ACF shortcode cannot display fields from non-public posts]'=>'[Шорткод ACF не может отображать поля из непубличных записей]','[The ACF shortcode is disabled on this site]'=>'[Шорткод ACF отключен на этом сайте]','Businessman Icon'=>'Иконка Бизнесмен','Forums Icon'=>'Иконка Форумы','YouTube Icon'=>'Иконка YouTube','Yes (alt) Icon'=>'Иконка Да (alt)','Xing Icon'=>'Иконка Xing','WordPress (alt) Icon'=>'Иконка WordPress (alt)','WhatsApp Icon'=>'Иконка WhatsApp','View Site Icon'=>'Иконка Показать сайт','Learn More Icon'=>'Иконка Узнать больше','Add Page Icon'=>'Иконка Добавить страницу','Video (alt3) Icon'=>'Иконка Видео (alt3)','Video (alt2) Icon'=>'Иконка Видео (alt2)','Video (alt) Icon'=>'Иконка Видео (alt)','Update (alt) Icon'=>'Иконка Обновить (alt)','Universal Access (alt) Icon'=>'Иконка Универсальный доступ (alt)','Twitter (alt) Icon'=>'Иконка Twitter (alt)','Twitch Icon'=>'Иконка Twitch','Tide Icon'=>'Иконка Tide','Tickets (alt) Icon'=>'Иконка Билеты (alt)','Text Page Icon'=>'Иконка Текстовая страница','Table Row Delete Icon'=>'Иконка Удалить строку таблицы','Table Row Before Icon'=>'Иконка Перед строкой таблицы','Table Row After Icon'=>'Иконка После строки таблицы','Table Col Delete Icon'=>'Иконка Удалить колонку таблицы','Table Col Before Icon'=>'Иконка Перед колонкой таблицы','Table Col After Icon'=>'Иконка После колонки таблицы','Superhero (alt) Icon'=>'Иконка Супергерой (alt)','Superhero Icon'=>'Иконка Супергерой','Spotify Icon'=>'Иконка Spotify','Shortcode Icon'=>'Иконка Шорткод','Shield (alt) Icon'=>'Иконка Щит (alt)','Share (alt2) Icon'=>'Иконка Поделиться (alt2)','Share (alt) Icon'=>'Иконка Поделиться (alt)','Saved Icon'=>'Иконка Сохранено','RSS Icon'=>'Иконка RSS','REST API Icon'=>'Иконка REST API','Remove Icon'=>'Иконка Удалить','Reddit Icon'=>'Ааываыва','Privacy Icon'=>'Иконка Приватность','Printer Icon'=>'Иконка Принтер','Podio Icon'=>'Иконка Радио','Plus (alt2) Icon'=>'Иконка Плюс (alt2)','Plus (alt) Icon'=>'Иконка Плюс (alt)','Plugins Checked Icon'=>'Значок: Плагины проверены','Pinterest Icon'=>'Иконка Pinterest','Pets Icon'=>'Иконка Питомцы','PDF Icon'=>'Иконка PDF','Palm Tree Icon'=>'Иконка Пальмовое дерево','Open Folder Icon'=>'Иконка Открыть папку','No (alt) Icon'=>'Иконка Нет (alt)','Money (alt) Icon'=>'Иконка Деньги (alt)','Menu (alt3) Icon'=>'Иконка Меню (alt3)','Menu (alt2) Icon'=>'Иконка Меню (alt2)','Menu (alt) Icon'=>'Иконка Меню (alt)','Spreadsheet Icon'=>'Значок: Электронная таблица','Interactive Icon'=>'Значок: Интерактивный','Document Icon'=>'Иконка Документ','Default Icon'=>'Иконка По умолчанию','Location (alt) Icon'=>'Иконка Местоположение (alt)','LinkedIn Icon'=>'Иконка LinkedIn','Instagram Icon'=>'Иконка Instagram','Insert Before Icon'=>'Иконка Вставить перед','Insert After Icon'=>'Иконка Вставить после','Insert Icon'=>'Иконка Вставить','Info Outline Icon'=>'Значок: Инфо (обведен)','Images (alt2) Icon'=>'Иконка Изображения (alt2)','Images (alt) Icon'=>'Иконка Изображения (alt)','Rotate Right Icon'=>'Иконка Повернуть вправо','Rotate Left Icon'=>'Иконка Повернуть влево','Rotate Icon'=>'Иконка Повернуть','Flip Vertical Icon'=>'Иконка Отразить по вертикали','Flip Horizontal Icon'=>'Иконка Отразить по горизонтали','Crop Icon'=>'Иконка Обрезать','ID (alt) Icon'=>'Иконка ID (alt)','HTML Icon'=>'Иконка HTML','Hourglass Icon'=>'Значок: песочные часы','Heading Icon'=>'Значок заголовка','Google Icon'=>'Иконка Google','Games Icon'=>'Иконка Игры','Fullscreen Exit (alt) Icon'=>'Иконка Выйти из полноэкранного режима','Fullscreen (alt) Icon'=>'Иконка Полноэкранный режим (alt)','Status Icon'=>'Иконка Статус','Image Icon'=>'Иконка Изображение','Gallery Icon'=>'Иконка Галерея','Chat Icon'=>'Иконка Чат','Audio Icon'=>'Иконка Аудио','Aside Icon'=>'Иконка Сайдбар','Food Icon'=>'Иконка Еда','Exit Icon'=>'Иконка Выйти','Excerpt View Icon'=>'Иконка Отрывок','Embed Video Icon'=>'Иконка Встроенное видео','Embed Post Icon'=>'Иконка Встроенная запись','Embed Photo Icon'=>'Иконка Встроенное фото','Embed Generic Icon'=>'Значок: Встроеное общее содержимое','Embed Audio Icon'=>'Иконка Встроенное аудио','Email (alt2) Icon'=>'Иконка Email (alt2)','Ellipsis Icon'=>'Значок: Многоточие','Unordered List Icon'=>'Значок: Неупорядоченный список','RTL Icon'=>'Значок: RTL','Ordered List RTL Icon'=>'Значок: Упорядоченный список RTL','Ordered List Icon'=>'Значок: Сортированный список','LTR Icon'=>'Значок: LTR','Custom Character Icon'=>'Значок: Спецсимвол','Edit Page Icon'=>'Иконка Редактировать страницу','Edit Large Icon'=>'Значок: Редактировать (большой)','Drumstick Icon'=>'Иконка Барабанная палочка','Database View Icon'=>'Иконка Отобразить базу данных','Database Remove Icon'=>'Иконка Удалить базу данных','Database Import Icon'=>'Иконка Импорт базы данных','Database Export Icon'=>'Иконка Экспорт базы данных','Database Add Icon'=>'Иконка Добавить базу данных','Database Icon'=>'Иконка База данных','Cover Image Icon'=>'Значок: Изображение-обложка','Volume On Icon'=>'Иконка Включить звук','Volume Off Icon'=>'Иконка Выключить звук','Skip Forward Icon'=>'Значок: Перемотать вперёд','Skip Back Icon'=>'Значок: Перемотать назад','Repeat Icon'=>'Иконка Повторить','Play Icon'=>'Иконка Воспроизвести','Pause Icon'=>'Иконка Пауза','Forward Icon'=>'Иконка Вперед','Back Icon'=>'Иконка Назад','Columns Icon'=>'Иконка Колонки','Color Picker Icon'=>'Иконка Подборщик цвета','Coffee Icon'=>'Иконка Кофе','Code Standards Icon'=>'Иконка Стандарты кода','Cloud Upload Icon'=>'Иконка Загрузить в облако','Cloud Saved Icon'=>'Иконка Сохранено в облаке','Car Icon'=>'Иконка Машина','Camera (alt) Icon'=>'Иконка Камера (alt)','Calculator Icon'=>'Иконка Калькулятор','Button Icon'=>'Иконка Кнопка','Businessperson Icon'=>'Значок: Бизнесмен','Tracking Icon'=>'Значок: Отслеживание','Topics Icon'=>'Иконка Темы','Replies Icon'=>'Значок: Ответы','PM Icon'=>'Иконка PM','Friends Icon'=>'Иконка Друзья','Community Icon'=>'Иконка Сообщество','BuddyPress Icon'=>'Иконка BuddyPress','bbPress Icon'=>'Иконка bbPress','Activity Icon'=>'Иконка Активность','Book (alt) Icon'=>'Иконка Книга (alt)','Block Default Icon'=>'Значок: Блок по умолчанию','Bell Icon'=>'Иконка Колокол','Beer Icon'=>'Иконка Пиво','Bank Icon'=>'Иконка Банк','Arrow Up (alt2) Icon'=>'Значок: Стрелка вверх (alt2)','Arrow Up (alt) Icon'=>'Значок: Стрелка вверх (alt)','Arrow Right (alt2) Icon'=>'Значок: Стрелка вправо (alt2)','Arrow Right (alt) Icon'=>'Значок: Стрелка вправо (alt)','Arrow Left (alt2) Icon'=>'Значок: Стрелка влево (alt2)','Arrow Left (alt) Icon'=>'Значок: Стрелка влево (alt)','Arrow Down (alt2) Icon'=>'Значок: Стрелка вниз (alt2)','Arrow Down (alt) Icon'=>'Значок: Стрелка вниз (alt)','Amazon Icon'=>'Иконка Amazon','Align Wide Icon'=>'Значок: Выравнивание по ширине','Align Pull Right Icon'=>'Значок: Выравнивание по правому краю','Align Pull Left Icon'=>'Значок: Выравнивание по левому краю','Align Full Width Icon'=>'Значок: Выравнивание по всей ширине','Airplane Icon'=>'Иконка Самолет','Site (alt3) Icon'=>'Иконка Сайт (alt3)','Site (alt2) Icon'=>'Иконка Сайт (alt2)','Site (alt) Icon'=>'Иконка Сайт (alt)','Upgrade to ACF PRO to create options pages in just a few clicks'=>'Обновитесь до ACF PRO чтобы создавать страницы настроек в пару кликов','Invalid request args.'=>'Неверные аргументы запроса.','Sorry, you do not have permission to do that.'=>'Извините, у вас нет разрешения чтобы делать это.','Blocks Using Post Meta'=>'Блоки, использующие мета данные записей','ACF PRO logo'=>'Логотип ACF PRO','ACF PRO Logo'=>'Логотип ACF PRO','%s requires a valid attachment ID when type is set to media_library.'=>'%s требует валидный ID вложения когда установлен тип media_library.','%s is a required property of acf.'=>'%s является обязательным свойством acf.','The value of icon to save.'=>'Значение иконки для сохранения.','The type of icon to save.'=>'Тип иконки для сохранения.','Yes Icon'=>'Иконка Да','WordPress Icon'=>'Иконка WordPress','Warning Icon'=>'Иконка Предупреждение','Visibility Icon'=>'Иконка Видимость','Vault Icon'=>'Иконка Хранилище','Upload Icon'=>'Иконка Загрузить','Update Icon'=>'Иконка Обновить','Unlock Icon'=>'Иконка Разблокировать','Universal Access Icon'=>'Иконка Универсальный доступ','Undo Icon'=>'Иконка Отменить','Twitter Icon'=>'Иконка Twitter','Trash Icon'=>'Иконка Мусорка','Translation Icon'=>'Иконка Перевод','Tickets Icon'=>'Иконка Билеты','Thumbs Up Icon'=>'Значок: Палец вверх','Thumbs Down Icon'=>'Значок: Палец вниз','Text Icon'=>'Иконка Текст','Testimonial Icon'=>'Значок: Отзыв','Tagcloud Icon'=>'Иконка Облако меток','Tag Icon'=>'Иконка Метка','Tablet Icon'=>'Иконка Планшет','Store Icon'=>'Иконка Магазин','Sticky Icon'=>'Значок: Закрепленный','Star Half Icon'=>'Иконка Звезда половина','Star Filled Icon'=>'Иконка Звезда заполненная','Star Empty Icon'=>'Иконка Звезда пустая','Sos Icon'=>'Значок: SOS','Sort Icon'=>'Иконка Сортировать','Smiley Icon'=>'Значок: Смайлик','Smartphone Icon'=>'Иконка Смартфон','Slides Icon'=>'Иконка Слайды','Shield Icon'=>'Иконка Щит','Share Icon'=>'Иконка Поделиться','Search Icon'=>'Иконка Поиск','Screen Options Icon'=>'Значок: Настройки экрана','Schedule Icon'=>'Иконка Расписание','Redo Icon'=>'Иконка Повторить','Randomize Icon'=>'Значок: Перемешать','Products Icon'=>'Иконка Товары','Pressthis Icon'=>'Значок: Опубликовать','Post Status Icon'=>'Иконка Статус записи','Portfolio Icon'=>'Иконка Портфолио','Plus Icon'=>'Иконка Плюс','Playlist Video Icon'=>'Иконка Видео плейлист','Playlist Audio Icon'=>'Иконка Аудио плейлист','Phone Icon'=>'Иконка Телефон','Performance Icon'=>'Значок: Производительность','Paperclip Icon'=>'Значок: Скрепка','No Icon'=>'Иконка Нет','Networking Icon'=>'Значок: Сеть','Nametag Icon'=>'Значок: Бейдж','Move Icon'=>'Иконка Переместить','Money Icon'=>'Иконка Деньги','Minus Icon'=>'Иконка Минус','Migrate Icon'=>'Иконка Перенос','Microphone Icon'=>'Иконка Микрофон','Megaphone Icon'=>'Иконка Мегафон','Marker Icon'=>'Иконка Маркер','Lock Icon'=>'Иконка Закрыть','Location Icon'=>'Иконка Местоположение','List View Icon'=>'Значок: Просмотр списка','Lightbulb Icon'=>'Иконка Лампочка','Left Right Icon'=>'Значок: Слева направо','Layout Icon'=>'Значок: Макет','Laptop Icon'=>'Иконка Ноутбук','Info Icon'=>'Иконка Инфо','Index Card Icon'=>'Значок: Картотека','ID Icon'=>'Иконка ID','Hidden Icon'=>'Значок: Скрытый','Heart Icon'=>'Иконка Сердце','Hammer Icon'=>'Иконка Молот','Groups Icon'=>'Иконка Группы','Grid View Icon'=>'Значок: Просмотр сеткой','Forms Icon'=>'Иконка Формы','Flag Icon'=>'Иконка Флаг','Filter Icon'=>'Иконка Фильтр','Feedback Icon'=>'Иконка Обратная связь','Facebook (alt) Icon'=>'Иконка Facebook (alt)','Facebook Icon'=>'Иконка Facebook','External Icon'=>'Значок: Внешний','Email (alt) Icon'=>'Иконка Email (alt)','Email Icon'=>'Иконка Email','Video Icon'=>'Иконка Видео','Unlink Icon'=>'Значок: Отвязать ссылку','Underline Icon'=>'Значок: Подчеркнутый','Text Color Icon'=>'Значок: Цвет текста','Table Icon'=>'Иконка Таблица','Strikethrough Icon'=>'Значок: Зачеркнуто','Spellcheck Icon'=>'Значок: Проверка правописания','Remove Formatting Icon'=>'Значок: Удалить форматирование','Quote Icon'=>'Иконка Цитата','Paste Word Icon'=>'Иконка Вставить слово','Paste Text Icon'=>'Иконка Вставить текст','Paragraph Icon'=>'Иконка Параграф','Outdent Icon'=>'Значок: Отступ слева','Kitchen Sink Icon'=>'Значок: Кухонная раковина','Justify Icon'=>'Значок: Выравнивание','Italic Icon'=>'Иконка Курсив','Insert More Icon'=>'Значок: Вставить больше','Indent Icon'=>'Значок: Отступ справа','Help Icon'=>'Иконка Помощь','Expand Icon'=>'Иконка Развернуть','Contract Icon'=>'Значок: Сжать','Code Icon'=>'Иконка Код','Break Icon'=>'Значок: Разбить','Bold Icon'=>'Иконка Жирный','Edit Icon'=>'Иконка Редактировать','Download Icon'=>'Значок: Скачать','Dismiss Icon'=>'Значок: отклонить','Desktop Icon'=>'Значок: рабочий стол','Dashboard Icon'=>'Значок: консоль','Cloud Icon'=>'Иконка Облако','Clock Icon'=>'Значок: Часы','Clipboard Icon'=>'Значок: буфер обмена','Chart Pie Icon'=>'Значок: Круговая диаграмма','Chart Line Icon'=>'Значок: Линейный график','Chart Bar Icon'=>'Значок: Гистограмма','Chart Area Icon'=>'Значок: Диаграмма площади','Category Icon'=>'Значок: рубрика','Cart Icon'=>'Иконка Корзина','Carrot Icon'=>'Иконка Морковка','Camera Icon'=>'Иконка Камера','Calendar (alt) Icon'=>'Значок: Календарь (alt)','Calendar Icon'=>'Значок: календарь','Businesswoman Icon'=>'Значок: Бизнесвумен','Building Icon'=>'Значок: Здание','Book Icon'=>'Иконка Книга','Backup Icon'=>'Иконка Резервная копия','Awards Icon'=>'Иконка Награды','Art Icon'=>'Значок: арт объект','Arrow Up Icon'=>'Значок: стрелка вверх','Arrow Right Icon'=>'Значок: Стрелка вправо','Arrow Left Icon'=>'Значок: Стрелка влево','Arrow Down Icon'=>'Значок: стрелка вниз','Archive Icon'=>'Значок: Архив','Analytics Icon'=>'Значок: Аналитика','Align Right Icon'=>'Значок: Выравнивание по правому краю','Align None Icon'=>'Значок: Без выравнивания','Align Left Icon'=>'Значок: Выравнивание по левому краю','Align Center Icon'=>'Значок: Выравнивание по центру','Album Icon'=>'Иконка Альбом','Users Icon'=>'Иконка Пользователи','Tools Icon'=>'Иконка Инструменты','Site Icon'=>'Иконка Сайт','Settings Icon'=>'Иконка Настройки','Post Icon'=>'Иконка Запись','Plugins Icon'=>'Иконка Плагины','Page Icon'=>'Иконка Страница','Network Icon'=>'Иконка Сеть','Multisite Icon'=>'Иконка Мультисайт','Media Icon'=>'Иконка Медиа','Links Icon'=>'Иконка Ссылки','Home Icon'=>'Иконка Дом','Customizer Icon'=>'Значок: персонализация','Comments Icon'=>'Иконка Комментарии','Collapse Icon'=>'Иконка Свернуть','Appearance Icon'=>'Иконка Внешний вид','Generic Icon'=>'Значок: общий','Icon picker requires a value.'=>'Подборщик иконки требует значение.','Icon picker requires an icon type.'=>'Подборщик иконки требует тип иконки.','The available icons matching your search query have been updated in the icon picker below.'=>'Доступные иконки, соответствующие вашему поисковому запросу, были обновлены в подборщике иконки ниже.','No results found for that search term'=>'Не найдено результатов по этому поисковому запросу','Array'=>'Массив','String'=>'Строка','Specify the return format for the icon. %s'=>'Укажите формат возвращаемого значения для иконки. %s','Select where content editors can choose the icon from.'=>'Укажите откуда редакторы содержимого могут выбирать иконку.','The URL to the icon you\'d like to use, or svg as Data URI'=>'URLиконки, которую вы хотите использовать или svg в качестве URI данных','Browse Media Library'=>'Открыть библиотеку файлов','The currently selected image preview'=>'Предпросмотр выбранного изображения','Click to change the icon in the Media Library'=>'Нажмите чтобы сменить иконку в библиотеке файлов','Search icons...'=>'Искать иконки...','Media Library'=>'Медиафайлы','Dashicons'=>'Dashicons','An interactive UI for selecting an icon. Select from Dashicons, the media library, or a standalone URL input.'=>'Интерактивный интерфейс для подбора иконки. Выбирайте среди Dashicons, из библиотеки файлов или используйте автономный ввод URL.','Icon Picker'=>'Подборщик иконки','JSON Load Paths'=>'Пути загрузки JSON','JSON Save Paths'=>'Пути сохранения JSON','Registered ACF Forms'=>'Зарегистрированные ACF формы','Shortcode Enabled'=>'Шорткод включен','Field Settings Tabs Enabled'=>'Вкладки настроек поля включены','Field Type Modal Enabled'=>'Окно типа поля включено','Admin UI Enabled'=>'Интерфейс администратора включен','Block Preloading Enabled'=>'Предварительная загрузка блока включена','Blocks Per API Version'=>'Блоки по версии API','Registered ACF Blocks'=>'Зарегистрированные ACF блоки','Light'=>'Светлый','Standard'=>'Стадартный','REST API Format'=>'Формат REST API','Registered Options Pages (PHP)'=>'Зарегистрированные страницы настроек (PHP)','Registered Options Pages (JSON)'=>'Зарегистрированные страницы настроек (JSON)','Registered Options Pages (UI)'=>'Зарегистрированные страницы настроек (UI)','Options Pages UI Enabled'=>'Интерфейс страниц настроек включен','Registered Taxonomies (JSON)'=>'Зарегистрированные таксономии (JSON)','Registered Taxonomies (UI)'=>'Зарегистрированные таксономии (UI)','Registered Post Types (JSON)'=>'Зарегистрированные типы записей (JSON)','Registered Post Types (UI)'=>'Зарегистрированные типы записей (UI)','Post Types and Taxonomies Enabled'=>'Типы записей и таксономии включены','Number of Third Party Fields by Field Type'=>'Кол-во сторонних полей по типу поля','Number of Fields by Field Type'=>'Кол-во полей по типу поля','Field Groups Enabled for GraphQL'=>'Группы полей включены для GraphQL','Field Groups Enabled for REST API'=>'Группы полей включены для REST API','Registered Field Groups (JSON)'=>'Зарегистрированные группы полей (JSON)','Registered Field Groups (PHP)'=>'Зарегистрированные группы полей (PHP)','Registered Field Groups (UI)'=>'Зарегистрированные группы полей (UI)','Active Plugins'=>'Активные плагины','Parent Theme'=>'Родительская тема','Active Theme'=>'Активная тема','Is Multisite'=>'Является мультисайтом','MySQL Version'=>'Версия MySQL','WordPress Version'=>'Версия WordPress','Subscription Expiry Date'=>'Дата окончания подписки','License Status'=>'Статус лицензии','License Type'=>'Тип лицензии','Licensed URL'=>'URL лицензии','License Activated'=>'Лицензия активирована','Free'=>'Бесплатный','Plugin Type'=>'Тип плагина','Plugin Version'=>'Версия плагина','This section contains debug information about your ACF configuration which can be useful to provide to support.'=>'В этом разделе содержится отладочная информация о конфигурации ACF, которая может быть полезна для предоставления в службу поддержки.','An ACF Block on this page requires attention before you can save.'=>'Блок ACF на этой странице требует внимания, прежде чем вы сможете сохранить.','This data is logged as we detect values that have been changed during output. %1$sClear log and dismiss%2$s after escaping the values in your code. The notice will reappear if we detect changed values again.'=>'Эти данные записываются в журнал, когда мы обнаруживаем значения, которые были изменены во время вывода. %1$sОчистите журнал и закройте уведомление%2$s после экранирования значений в вашем коде. Уведомление появится снова, если мы снова обнаружим измененные значения.','Dismiss permanently'=>'Отклонить навсегда','Instructions for content editors. Shown when submitting data.'=>'Инструкции для редакторов. Отображается при отправке данных.','Has no term selected'=>'Нет выбранных терминов','Has any term selected'=>'Имеет выбранный термин','Terms do not contain'=>'Термин не содержит','Terms contain'=>'Термины содержат','Term is not equal to'=>'Термин не равен','Term is equal to'=>'Термин равен','Has no user selected'=>'Нет выбранных пользователей','Has any user selected'=>'Имеет выбранного пользователя','Users do not contain'=>'Пользователь не содержит','Users contain'=>'Пользователи содержат','User is not equal to'=>'Пользователь не равен','User is equal to'=>'Пользователь равен','Has no page selected'=>'Нет выбранных страниц','Has any page selected'=>'Имеет выбранную страницу','Pages do not contain'=>'Страница не содержит','Pages contain'=>'Страницы содержат','Page is not equal to'=>'Запись не равно','Page is equal to'=>'Запись не равна','Has no relationship selected'=>'Не выбрана ни одна взаимосвязь','Has any relationship selected'=>'Имеет выбранную взаимосвязь','Has no post selected'=>'Нет выбранных записей','Has any post selected'=>'Имеет выбранную запись','Posts do not contain'=>'Запись не содержит','Posts contain'=>'Записи содержат','Post is not equal to'=>'Запись не равна','Post is equal to'=>'Запись равна','Relationships do not contain'=>'Взаимосвязь не содержит','Relationships contain'=>'Взаимосвязь содержит','Relationship is not equal to'=>'Взаимосвязь не равна','Relationship is equal to'=>'Взаимосвязь равна','The core ACF block binding source name for fields on the current pageACF Fields'=>'Поля ACF','ACF PRO Feature'=>'Функция ACF PRO','Renew PRO to Unlock'=>'Возобновить PRO чтобы разблокировать','Renew PRO License'=>'Возобновить лицензию PRO','PRO fields cannot be edited without an active license.'=>'PRO-поля не могут быть отредактированы без активной лицензии','Please activate your ACF PRO license to edit field groups assigned to an ACF Block.'=>'Пожалуйста, активируйте лицензию ACF PRO чтобы редактировать группы полей которые привязаны к ACF-блокам.','Please activate your ACF PRO license to edit this options page.'=>'Пожалуйста, активируйте вашу лицензию ACF PRO чтобы редактировать эту страницу настроек.','Returning escaped HTML values is only possible when format_value is also true. The field values have not been returned for security.'=>'Возврат экранированных значений HTML возможен только если "format_value" также равен "true". Значение поля не будет возвращено в целях безопасности.','Returning an escaped HTML value is only possible when format_value is also true. The field value has not been returned for security.'=>'Возврат экранированного значения HTML возможен только если "format_value" также равен "true". Значение поля не будет возвращено в целях безопасности.','Please contact your site administrator or developer for more details.'=>'Пожалуйста, свяжитесь с вашим администратором сайта или разработчиком чтобы узнать подробности.','Learn&nbsp;more'=>'Узнать&nbsp;больше','Hide&nbsp;details'=>'Скрыть&nbsp;подробности','Show&nbsp;details'=>'Показать&nbsp;подробности','%1$s (%2$s) - rendered via %3$s'=>'%1$s (%2$s) - отрисовано через %3$s','Renew ACF PRO License'=>'Возобновить лицензию ACF PRO','Renew License'=>'Продлить лицензию','Manage License'=>'Управление лицензией','\'High\' position not supported in the Block Editor'=>'Расположение \'Сверху\' не поддерживается в редакторе блоков','Upgrade to ACF PRO'=>'Обновить до ACF PRO','Add Options Page'=>'Добавить страницу настроек','In the editor used as the placeholder of the title.'=>'Используется как текст-заполнитель заголовка в редакторе.','Title Placeholder'=>'Текст-заполнитель заголовка','4 Months Free'=>'4 месяца бесплатно','(Duplicated from %s)'=>'(Скопировано из %s)','Select Options Pages'=>'Выберите страницы настроек','Duplicate taxonomy'=>'Скопировать таксономию','Create taxonomy'=>'Создать таксономию','Duplicate post type'=>'Скопировать тип записи','Create post type'=>'Создать тип записи','Link field groups'=>'Привязать группы полей','Add fields'=>'Добавить поля','This Field'=>'Это поле','ACF PRO'=>'ACF (PRO)','Feedback'=>'Обратная связь','Support'=>'Поддержка','is developed and maintained by'=>'разработан и поддерживается','Add this %s to the location rules of the selected field groups.'=>'Добавьте %s в правила местонахождения выбранных групп полей.','Select field(s) to store the reference back to the item being updated. You may select this field. Target fields must be compatible with where this field is being displayed. For example, if this field is displayed on a Taxonomy, your target field should be of type Taxonomy'=>'Выберите поле(-я) для хранения ссылки на обновляемый элемент. Вы можете выбрать это поле. Целевые поля должны быть совместимы с тем, где отображается это поле. Например, если это поле отображается в таксономии, целевое поле должно иметь тип Таксономия','Target Field'=>'Целевое поле','Update a field on the selected values, referencing back to this ID'=>'Обновлять поле по выбранным значениям, ссылаясь на этот ID','Bidirectional'=>'Двунаправленный','%s Field'=>'%s поле','Select Multiple'=>'Выбор нескольких пунктов','WP Engine logo'=>'Логотип WP Engine','Lower case letters, underscores and dashes only, Max 32 characters.'=>'Только буквы нижнего регистра, подчеркивания и дефисы. Максимум 32 символа.','The capability name for assigning terms of this taxonomy.'=>'Названия прав доступа для присвоения терминов к этой таксономии.','Assign Terms Capability'=>'Права для присвоения терминов','The capability name for deleting terms of this taxonomy.'=>'Названия прав доступа для удаления терминов этой таксономии.','Delete Terms Capability'=>'Права для удаления терминов','The capability name for editing terms of this taxonomy.'=>'Названия прав доступа для редактирования терминов этой таксономии.','Edit Terms Capability'=>'Права для редактирования терминов','The capability name for managing terms of this taxonomy.'=>'Название прав доступа для управления терминами этой таксономии.','Manage Terms Capability'=>'Возможность управления терминами','Sets whether posts should be excluded from search results and taxonomy archive pages.'=>'Устанавливает, должны ли записи быть исключены из результатов поиска и страниц архива таксономии.','More Tools from WP Engine'=>'Больше инструментов от WP Engine','View Pricing & Upgrade'=>'Просмотр цен и обновление','Learn More'=>'Читать больше','Unlock Advanced Features and Build Even More with ACF PRO'=>'Разблокируйте дополнительные функции и сделайте больше с ACF PRO','%s fields'=>'%s поля','No terms'=>'Нет терминов','No post types'=>'Нет типов записей','No posts'=>'Нет записей','No taxonomies'=>'Нет таксономий','No field groups'=>'Нет групп полей','No fields'=>'Нет полей','No description'=>'Нет описания','Any post status'=>'Любой статус записи','This taxonomy key is already in use by another taxonomy registered outside of ACF and cannot be used.'=>'Указанный ключ таксономии уже используется другой таксономией, зарегистрированной вне ACF, и не может быть использован.','This taxonomy key is already in use by another taxonomy in ACF and cannot be used.'=>'Указанный ключ таксономии уже используется другой таксономией в ACF и не может быть использован.','The taxonomy key must only contain lower case alphanumeric characters, underscores or dashes.'=>'Ключ таксономии должен содержать только буквенно-цифровые символы в нижнем регистре, знак подчеркивания или тире.','The taxonomy key must be under 32 characters.'=>'Ключ таксономии должен содержать не более 32 символов.','No Taxonomies found in Trash'=>'В корзине не найдено ни одной таксономии','No Taxonomies found'=>'Таксономии не найдены','Search Taxonomies'=>'Найти таксономии','View Taxonomy'=>'Смотреть таксономию','New Taxonomy'=>'Новая таксономия','Edit Taxonomy'=>'Править таксономию','Add New Taxonomy'=>'Добавить новую таксономию','No Post Types found in Trash'=>'В корзине не найдено ни одного типа записей','No Post Types found'=>'Типы записей не найдены','Search Post Types'=>'Найти типы записей','View Post Type'=>'Смотреть тип записи','New Post Type'=>'Новый тип записи','Edit Post Type'=>'Изменить тип записи','Add New Post Type'=>'Добавить новый тип записи','This post type key is already in use by another post type registered outside of ACF and cannot be used.'=>'Указанный ключ типа записи уже используется другим типом записи, зарегистрированным вне ACF, и не может быть использован.','This post type key is already in use by another post type in ACF and cannot be used.'=>'Указанный ключ типа записи уже используется другим типом записи в ACF и не может быть использован.','This field must not be a WordPress <a href="%s" target="_blank">reserved term</a>.'=>'Это поле является <a href="%s" target="_blank">зарезервированным термином</a> WordPress.','The post type key must only contain lower case alphanumeric characters, underscores or dashes.'=>'Ключ типа записи должен содержать только буквенно-цифровые символы в нижнем регистре, знак подчеркивания или тире.','The post type key must be under 20 characters.'=>'Ключ типа записи должен содержать не более 20 символов.','We do not recommend using this field in ACF Blocks.'=>'Мы не рекомендуем использовать это поле в блоках ACF.','Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing for a rich text-editing experience that also allows for multimedia content.'=>'Показывает WordPress WYSIWYG редактор такой же, как в Записях или Страницах и позволяет редактировать текст, а также мультимедийное содержимое.','WYSIWYG Editor'=>'WYSIWYG редактор','Allows the selection of one or more users which can be used to create relationships between data objects.'=>'Позволяет выбрать одного или нескольких пользователей, которые могут быть использованы для создания взаимосвязей между объектами данных.','A text input specifically designed for storing web addresses.'=>'Текстовый поле, специально разработанное для хранения веб-адресов.','URL'=>'URL','A toggle that allows you to pick a value of 1 or 0 (on or off, true or false, etc). Can be presented as a stylized switch or checkbox.'=>'Переключатель, позволяющий выбрать значение 1 или 0 (включено или выключено, истинно или ложно и т.д.). Может быть представлен в виде стилизованного переключателя или флажка.','An interactive UI for picking a time. The time format can be customized using the field settings.'=>'Интерактивный пользовательский интерфейс для выбора времени. Формат времени можно настроить с помощью параметров поля.','A basic textarea input for storing paragraphs of text.'=>'Простая текстовая область для хранения абзацев текста.','A basic text input, useful for storing single string values.'=>'Простое текстовое поле, предназначенное для хранения однострочных значений.','Allows the selection of one or more taxonomy terms based on the criteria and options specified in the fields settings.'=>'Позволяет выбрать один или несколько терминов таксономии на основе критериев и параметров, указанных в настройках полей.','Allows you to group fields into tabbed sections in the edit screen. Useful for keeping fields organized and structured.'=>'Позволяет группировать поля в разделы с вкладками на экране редактирования. Полезно для упорядочивания и структурирования полей.','A dropdown list with a selection of choices that you specify.'=>'Выпадающий список с заданными вариантами выбора.','A dual-column interface to select one or more posts, pages, or custom post type items to create a relationship with the item that you\'re currently editing. Includes options to search and filter.'=>'Двухколоночный интерфейс для выбора одной или нескольких записей, страниц или элементов пользовательских типов записей, чтобы создать связь с элементом, который вы сейчас редактируете. Включает опции поиска и фильтрации.','An input for selecting a numerical value within a specified range using a range slider element.'=>'Поле выбора числового значения в заданном диапазоне с помощью ползунка диапазона.','A group of radio button inputs that allows the user to make a single selection from values that you specify.'=>'Группа радиокнопок, позволяющих пользователю выбрать одно из заданных вами значений.','An interactive and customizable UI for picking one or many posts, pages or post type items with the option to search. '=>'Интерактивный и настраиваемый интерфейс для выбора одной или нескольких записей, страниц или типов записей с возможностью поиска. ','An input for providing a password using a masked field.'=>'Ввод пароля с помощью замаскированного поля.','Filter by Post Status'=>'Фильтр по статусу записи','An interactive dropdown to select one or more posts, pages, custom post type items or archive URLs, with the option to search.'=>'Интерактивный выпадающий список для выбора одного или нескольких записей, страниц, записей пользовательского типа или URL-адресов архивов с возможностью поиска.','An interactive component for embedding videos, images, tweets, audio and other content by making use of the native WordPress oEmbed functionality.'=>'Интерактивный компонент для вставки видео, изображений, твитов, аудио и другого контента с использованием встроенной в WordPress функциональности oEmbed.','An input limited to numerical values.'=>'Ввод ограничен числовыми значениями.','Used to display a message to editors alongside other fields. Useful for providing additional context or instructions around your fields.'=>'Используется для отображения сообщения для редакторов рядом с другими полями. Полезно для предоставления дополнительного контекста или инструкций по работе с полями.','Allows you to specify a link and its properties such as title and target using the WordPress native link picker.'=>'Позволяет указать ссылку и ее свойства, такие как заголовок и цель, используя встроенный в WordPress подборщик ссылок.','Uses the native WordPress media picker to upload, or choose images.'=>'Использует встроенный в WordPress подборщик медиа для загрузки или выбора изображений.','Provides a way to structure fields into groups to better organize the data and the edit screen.'=>'Предоставляет возможность структурировать поля по группам, чтобы лучше организовать данные и экран редактирования.','An interactive UI for selecting a location using Google Maps. Requires a Google Maps API key and additional configuration to display correctly.'=>'Интерактивный интерфейс для выбора местоположения с помощью Google Maps. Требуется ключ Google Maps API и дополнительная настройка для корректного отображения.','Uses the native WordPress media picker to upload, or choose files.'=>'Использует встроенный в WordPress медиа подборщик чтобы загружать или выбирать файлы.','A text input specifically designed for storing email addresses.'=>'Текстовый ввод, специально предназначенный для хранения адресов электронной почты.','An interactive UI for picking a date and time. The date return format can be customized using the field settings.'=>'Интерактивный интерфейс для выбора времени. Формат времени можно настроить с помощью параметров поля.','An interactive UI for picking a date. The date return format can be customized using the field settings.'=>'Интерактивный интерфейс для выбора даты. Возвращаемый формат даты можно настроить с помощью параметров поля.','An interactive UI for selecting a color, or specifying a Hex value.'=>'Интерактивный интерфейс для выбора цвета или указания Hex значения.','A group of checkbox inputs that allow the user to select one, or multiple values that you specify.'=>'Группа полей с флажками, которые позволяют пользователю выбрать одно или несколько из заданных вами значений.','A group of buttons with values that you specify, users can choose one option from the values provided.'=>'Группа кнопок с указанными вами значениями, пользователи могут выбрать одну опцию из представленных значений.','Allows you to group and organize custom fields into collapsable panels that are shown while editing content. Useful for keeping large datasets tidy.'=>'Позволяет ваш группировать и организовывать пользовательские поля в сворачиваемые панели, которые отображаются при редактировании содержимого. Полезно для поддержания порядка в больших наборах данных.','This provides a solution for repeating content such as slides, team members, and call-to-action tiles, by acting as a parent to a set of subfields which can be repeated again and again.'=>'Обеспечивает решение для повторения содержимого, такого как слайды, члены команды и плитки с призывами к действию, выступая в качестве родителя для набора вложенных полей, которые можно повторять снова и снова.','This provides an interactive interface for managing a collection of attachments. Most settings are similar to the Image field type. Additional settings allow you to specify where new attachments are added in the gallery and the minimum/maximum number of attachments allowed.'=>'Интерактивный интерфейс для управления коллекцией вложений. Большинство настроек аналогично типу поля «Изображение». Дополнительные настройки позволяют указать место добавления новых вложений в галерею и мин/макс количество вложений.','This provides a simple, structured, layout-based editor. The Flexible Content field allows you to define, create and manage content with total control by using layouts and subfields to design the available blocks.'=>'Обеспечивает простой, структурированный редактор на основе макетов. Поле «Гибкое содержимое» позволяет создавать и управлять содержимым с полным контролем, используя макеты и вложенные поля для оформления доступных блоков.','This allows you to select and display existing fields. It does not duplicate any fields in the database, but loads and displays the selected fields at run-time. The Clone field can either replace itself with the selected fields or display the selected fields as a group of subfields.'=>'Позволяет выбирать и отображать существующие поля. Он не дублирует поля в базе данных, а загружает и отображает выбранные поля во время выполнения программы. Поле Дубликатор может либо заменять собой выбранные поля, либо отображать выбранные поля в виде группы вложенных полей.','nounClone'=>'Клонировать','PRO'=>'PRO','Advanced'=>'Дополнительно','JSON (newer)'=>'JSON (более новая версия)','Original'=>'Оригинальный','Invalid post ID.'=>'Неверный ID записи.','Invalid post type selected for review.'=>'Для просмотра выбран неверный тип записи.','More'=>'Читать далее','Tutorial'=>'Руководство','Select Field'=>'Выбрать поля','Try a different search term or browse %s'=>'Попробуйте другой поисковый запрос или просмотрите %s','Popular fields'=>'Популярные поля','No search results for \'%s\''=>'Нет результатов поиска для \'%s\'','Search fields...'=>'Поля поиска...','Select Field Type'=>'Выбрать тип поля','Popular'=>'Популярные','Add Taxonomy'=>'Добавить таксономию','Create custom taxonomies to classify post type content'=>'Создание пользовательских таксономий для классификации содержимого типов записей','Add Your First Taxonomy'=>'Добавьте свою первую таксономию','Hierarchical taxonomies can have descendants (like categories).'=>'Иерархические таксономии могут иметь потомков (например, категории).','Makes a taxonomy visible on the frontend and in the admin dashboard.'=>'Сделать видимой таксономию на фронтенде и в админпанели.','One or many post types that can be classified with this taxonomy.'=>'Один или несколько типов записей, которые можно классифицировать с помощью данной таксономии.','genre'=>'жанр','Genre'=>'Жанр','Genres'=>'Жанры','Optional custom controller to use instead of `WP_REST_Terms_Controller `.'=>'Пользовательский контроллер для использования вместо `WP_REST_Terms_Controller`.','Expose this post type in the REST API.'=>'Представить этот тип записей в REST API.','Customize the query variable name'=>'Настройте значение переменной запроса.','Terms can be accessed using the non-pretty permalink, e.g., {query_var}={term_slug}.'=>'Доступ к терминам можно получить, используя некрасивую постоянную ссылку, например {query_var}={term_slug}.','Parent-child terms in URLs for hierarchical taxonomies.'=>'Термины «родитель-ребенок» в URL для древовидных таксономий.','Customize the slug used in the URL'=>'Настроить слаг, используемое в URL','Permalinks for this taxonomy are disabled.'=>'Постоянные ссылки для этой таксономии отключены.','Rewrite the URL using the taxonomy key as the slug. Your permalink structure will be'=>'Перепишите URL, используя ключ таксономии в качестве слага. Ваша структура постоянных ссылок будет выглядеть следующим образом','Taxonomy Key'=>'Ключ таксономии','Select the type of permalink to use for this taxonomy.'=>'Выберите тип постоянной ссылки для этой таксономии.','Display a column for the taxonomy on post type listing screens.'=>'Отображать колонку таксономии на страницах списков типа записей.','Show Admin Column'=>'Отображать столбец админа','Show the taxonomy in the quick/bulk edit panel.'=>'Отображать таксономию в панели быстрого/массового редактирования.','Quick Edit'=>'Быстрое редактирование','List the taxonomy in the Tag Cloud Widget controls.'=>'Выводит таксономию в управлении виджета Облака меток.','Tag Cloud'=>'Облако меток','A PHP function name to be called for sanitizing taxonomy data saved from a meta box.'=>'Название PHP-функции, вызываемой для санации данных таксономии, сохраненных из мета-бокса.','Meta Box Sanitization Callback'=>'Колбэк-функция очистки данных метабокса','Register Meta Box Callback'=>'Регистрация обратного вызова метабокса','No Meta Box'=>'Отсутствует метабокс','Custom Meta Box'=>'Произвольный метабокс','Controls the meta box on the content editor screen. By default, the Categories meta box is shown for hierarchical taxonomies, and the Tags meta box is shown for non-hierarchical taxonomies.'=>'Управляет метабоксом на странице редактирования. По умолчанию для древовидных таксономий отображается метабокс Рубрики, а для недревовидных - метабокс Метки.','Meta Box'=>'Блок метаданных','Categories Meta Box'=>'Категории метабокса','Tags Meta Box'=>'Теги метабокса','A link to a tag'=>'Ссылка на метку','Describes a navigation link block variation used in the block editor.'=>'Описывает вариацию блока навигационных ссылок, используемому в редакторе блоков.','A link to a %s'=>'Ссылка на %s','Tag Link'=>'Ссылка метки','Assigns a title for navigation link block variation used in the block editor.'=>'Назначает заголовок вариации блока навигационных ссылок, используемому в редакторе блоков.','← Go to tags'=>'← Перейти к меткам','Assigns the text used to link back to the main index after updating a term.'=>'Назначает текст, используемый для обратной ссылки на основной индекс после обновления термина.','Back To Items'=>'Вернуться к элементам','← Go to %s'=>'← Перейти к %s','Tags list'=>'Список меток','Assigns text to the table hidden heading.'=>'Присваивает текст скрытому заголовку таблицы.','Tags list navigation'=>'Навигация по списку меток','Assigns text to the table pagination hidden heading.'=>'Присваивает текст скрытому заголовку таблицы пагинации.','Filter by category'=>'Фильтр по рубрике','Assigns text to the filter button in the posts lists table.'=>'Назначает текст для кнопки фильтрации в таблице списков записей.','Filter By Item'=>'Фильтр по элементу','Filter by %s'=>'Фильтр по %s','The description is not prominent by default; however, some themes may show it.'=>'Описание по умолчанию не отображается, однако некоторые темы могут его показывать.','Describes the Description field on the Edit Tags screen.'=>'Описывает поле «Описание» на странице редактирования меток.','Description Field Description'=>'Описание поля описания','Assign a parent term to create a hierarchy. The term Jazz, for example, would be the parent of Bebop and Big Band'=>'Назначьте родительский термин для создания иерархии. Термин "Джаз", например, будет родителем для "Бибопа" и "Биг-бэнда".','Describes the Parent field on the Edit Tags screen.'=>'Описывает поле «Родитель» на странице редактирования меток.','Parent Field Description'=>'Описание родительского поля','The "slug" is the URL-friendly version of the name. It is usually all lower case and contains only letters, numbers, and hyphens.'=>'«Ярлык» — это вариант названия, подходящий для URL. Обычно содержит только латинские буквы в нижнем регистре, цифры и дефисы.','Describes the Slug field on the Edit Tags screen.'=>'Описывает поле «Ярлык» на странице редактирования меток.','Slug Field Description'=>'Описание поля слага','The name is how it appears on your site'=>'Название определяет, как элемент будет отображаться на вашем сайте','Describes the Name field on the Edit Tags screen.'=>'Описывает поле «Название» на странице редактирования меток.','Name Field Description'=>'Описание имени слага','No tags'=>'Меток нет','Assigns the text displayed in the posts and media list tables when no tags or categories are available.'=>'Назначает текст, отображаемый в таблицах постов и списка медиафайлов при отсутствии тегов и категорий.','No Terms'=>'Нет терминов','No %s'=>'Нет %s','No tags found'=>'Метки не найдены','Assigns the text displayed when clicking the \'choose from most used\' text in the taxonomy meta box when no tags are available, and assigns the text used in the terms list table when there are no items for a taxonomy.'=>'Назначает текст, отображаемый при нажатии на кнопку «выбрать из часто используемых» в метабоксе таксономии, если метки отсутствуют, и назначает текст, используемый в таблице списка терминов, если для таксономии нет элементов.','Not Found'=>'Не найдено','Assigns text to the Title field of the Most Used tab.'=>'Назначает текст поля "Заголовок" вкладки "Часто используемые".','Most Used'=>'Часто используемое','Choose from the most used tags'=>'Выбрать из часто используемых меток','Assigns the \'choose from most used\' text used in the meta box when JavaScript is disabled. Only used on non-hierarchical taxonomies.'=>'Назначает текст «выбрать из наиболее используемых», используемый в метабоксе, когда JavaScript отключен. Используется только для неиерархических таксономий.','Choose From Most Used'=>'Выберите из наиболее часто используемых','Choose from the most used %s'=>'Выберите из наиболее часто используемых %s','Add or remove tags'=>'Добавить или удалить метки','Assigns the add or remove items text used in the meta box when JavaScript is disabled. Only used on non-hierarchical taxonomies'=>'Назначает текст добавления или удаления элементов, используемый в метабоксе, когда JavaScript отключен. Используется только для неиерархических таксономий','Add Or Remove Items'=>'Добавить или удалить элементы','Add or remove %s'=>'Добавить или удалить %s','Separate tags with commas'=>'Метки разделяются запятыми','Assigns the separate item with commas text used in the taxonomy meta box. Only used on non-hierarchical taxonomies.'=>'Назначает текст разделителя через запятую, используемый в метабоксе таксономии. Используется только для неиерархических таксономий.','Separate Items With Commas'=>'Разделять элементы запятыми','Separate %s with commas'=>'Разделять %s запятыми','Popular Tags'=>'Популярные метки','Assigns popular items text. Only used for non-hierarchical taxonomies.'=>'Назначает текст популярных элементов.','Popular Items'=>'Популярные элементы','Popular %s'=>'Популярные %s','Search Tags'=>'Поиск меток','Assigns search items text.'=>'Назначает текст элементов поиска.','Parent Category:'=>'Родительская рубрика:','Assigns parent item text, but with a colon (:) added to the end.'=>'Назначает текст родительского элемента, но с двоеточием (:) на конце.','Parent Item With Colon'=>'Родительский элемент через двоеточие','Parent Category'=>'Родительская рубрика','Assigns parent item text. Only used on hierarchical taxonomies.'=>'Назначает текст родительского элемента. Используется только для древовидных таксономий.','Parent Item'=>'Родительский элемент','Parent %s'=>'Родитель %s','New Tag Name'=>'Название новой метки','Assigns the new item name text.'=>'Присваивает новый текст названия элемента.','New Item Name'=>'Название нового элемента','New %s Name'=>'Новое %s название','Add New Tag'=>'Добавить новую метку','Assigns the add new item text.'=>'Назначает текст добавления элемента.','Update Tag'=>'Обновить метку','Assigns the update item text.'=>'Назначает текст обновления элемента.','Update Item'=>'Обновить элемент','Update %s'=>'Обновить %s','View Tag'=>'Просмотреть метку','In the admin bar to view term during editing.'=>'В панели администратора для просмотра термина при его редактировании.','Edit Tag'=>'Изменить метку','At the top of the editor screen when editing a term.'=>'Сверху страницы редактирования при изменении термина.','All Tags'=>'Все метки','Assigns the all items text.'=>'Назначает всем элементам текст.','Assigns the menu name text.'=>'Назначает текст названия меню.','Menu Label'=>'Этикетка меню','Active taxonomies are enabled and registered with WordPress.'=>'Активные таксономии включены и регистрируются в WordPress.','A descriptive summary of the taxonomy.'=>'Описание таксономии.','A descriptive summary of the term.'=>'Описание термина.','Term Description'=>'Описание термина','Single word, no spaces. Underscores and dashes allowed.'=>'Одно слово, без пробелов. Подчеркивания и тире разрешены.','Term Slug'=>'Ярлык термина','The name of the default term.'=>'Название термина по умолчанию.','Term Name'=>'Название термина','Create a term for the taxonomy that cannot be deleted. It will not be selected for posts by default.'=>'Создайте термин для таксономии, который не может быть удален. По умолчанию он не будет выбран для записей.','Default Term'=>'Термин по умолчанию','Whether terms in this taxonomy should be sorted in the order they are provided to `wp_set_object_terms()`.'=>'Должны ли термины в этой таксономии сортироваться в том порядке, в котором они представлены в `wp_set_object_terms()`.','Sort Terms'=>'Сортировать термины','Add Post Type'=>'Добавить тип записи','Expand the functionality of WordPress beyond standard posts and pages with custom post types.'=>'Расширьте функциональность WordPress за пределы стандартных записей и страниц с помощью пользовательских типов постов.','Add Your First Post Type'=>'Добавьте свой первый тип записи','I know what I\'m doing, show me all the options.'=>'Я знаю, что делаю, покажи мне все варианты.','Advanced Configuration'=>'Расширенная конфигурация','Hierarchical post types can have descendants (like pages).'=>'Иерархические типы записей могут иметь потомков (например, страницы).','Hierarchical'=>'Иерархическая','Visible on the frontend and in the admin dashboard.'=>'Отображается на сайте и в панели администратора.','Public'=>'Открыто','movie'=>'фильм','Lower case letters, underscores and dashes only, Max 20 characters.'=>'Только буквы нижнего регистра, подчеркивания и дефисы. Максимум 20 символов.','Movie'=>'Фильм','Singular Label'=>'Одиночная этикетка','Movies'=>'Фильмы','Plural Label'=>'Подпись множественного числа','Optional custom controller to use instead of `WP_REST_Posts_Controller`.'=>'Опциональный произвольный контроллер, который будет использоваться вместо `WP_REST_Posts_Controller`.','Controller Class'=>'Класс контроллера','The namespace part of the REST API URL.'=>'Часть пространства имен в URL-адресе REST API.','Namespace Route'=>'Пространство имен маршрута','The base URL for the post type REST API URLs.'=>'Базовый URL-адрес для URL-адресов REST API типа записей.','Base URL'=>'Базовый URL','Exposes this post type in the REST API. Required to use the block editor.'=>'Передает этот тип записей в REST API. Требуется для использования редактора блоков.','Show In REST API'=>'Показывать в REST API','Customize the query variable name.'=>'Настройте имя переменной запроса.','Query Variable'=>'Переменная запроса','No Query Variable Support'=>'Нет поддержки переменных запросов','Custom Query Variable'=>'Пользовательская переменная запроса','Items can be accessed using the non-pretty permalink, eg. {post_type}={post_slug}.'=>'Доступ к элементам можно получить, используя некрасивую постоянную ссылку, например {post_type}={post_slug}.','Query Variable Support'=>'Поддержка переменных запроса','URLs for an item and items can be accessed with a query string.'=>'Доступ к URL-адресам элемента и элементов можно получить с помощью строки запроса.','Publicly Queryable'=>'Публично запрашиваемый','Custom slug for the Archive URL.'=>'Пользовательский ярлык для URL-адреса архива.','Archive Slug'=>'Ярлык архива','Has an item archive that can be customized with an archive template file in your theme.'=>'Имеет архив записей, который может быть настроен с помощью файла шаблона архива вашей темы.','Archive'=>'Архив','Pagination support for the items URLs such as the archives.'=>'Поддержка пагинации для URL элементов, таких как архивы.','Pagination'=>'Разделение на страницы','RSS feed URL for the post type items.'=>'URL-адрес RSS-канала для элементов типа записей.','Feed URL'=>'URL фида','Alters the permalink structure to add the `WP_Rewrite::$front` prefix to URLs.'=>'Изменяет структуру постоянных ссылок для добавления префикса `WP_Rewrite::$front` к URL-адресам.','Front URL Prefix'=>'Префикс URL-адреса','Customize the slug used in the URL.'=>'Настройте ярлык, используемый в URL.','URL Slug'=>'Ярлык URL','Permalinks for this post type are disabled.'=>'Постоянные ссылки для этого типа записей отключены.','Rewrite the URL using a custom slug defined in the input below. Your permalink structure will be'=>'Перепишите URL, используя пользовательский ярлык, заданный в поле ниже. Ваша структура постоянных ссылок будет выглядеть следующим образом','No Permalink (prevent URL rewriting)'=>'Без постоянный ссылки (предотвращает перезапись URL)','Custom Permalink'=>'Произвольная постоянная ссылка','Post Type Key'=>'Ключ типа записи','Rewrite the URL using the post type key as the slug. Your permalink structure will be'=>'Перезаписывать URL, используя ключ типа записей в качестве ярлыка. Структура вашей постоянной ссылки будет','Permalink Rewrite'=>'Переписать постоянную ссылку','Delete items by a user when that user is deleted.'=>'Удаление элементов, созданных пользователем, при удалении этого пользователя.','Delete With User'=>'Удалить вместе с пользователем','Allow the post type to be exported from \'Tools\' > \'Export\'.'=>'Разрешить экспорт типа сообщения из «Инструменты»> «Экспорт».','Can Export'=>'Можно экспортировать','Optionally provide a plural to be used in capabilities.'=>'При необходимости укажите множественное число, которое будет использоваться в правах доступа.','Plural Capability Name'=>'Название прав доступа во множественном числе','Choose another post type to base the capabilities for this post type.'=>'Выберите другой тип записи, чтобы использовать возможности этого типа записи.','Singular Capability Name'=>'Название прав доступа в единственном числе','By default the capabilities of the post type will inherit the \'Post\' capability names, eg. edit_post, delete_posts. Enable to use post type specific capabilities, eg. edit_{singular}, delete_{plural}.'=>'По умолчанию права доступа типа записей наследуют названия прав доступа у "Запись", например, edit_post, delete_posts. Включите, чтобы использовать характерные названия для типа записей, например edit_{ед_число}, delete_{множ_число}.','Rename Capabilities'=>'Переименование возможностей','Exclude From Search'=>'Исключить из поиска','Allow items to be added to menus in the \'Appearance\' > \'Menus\' screen. Must be turned on in \'Screen options\'.'=>'Разрешить добавлять элементы в меню «Внешний вид» > «Меню». Должно быть включено в «Настройках экрана».','Appearance Menus Support'=>'Поддержка меню внешнего вида','Appears as an item in the \'New\' menu in the admin bar.'=>'Появится как пункт в меню \'Добавить\' на панели администратора.','Show In Admin Bar'=>'Показать на панели админа','Custom Meta Box Callback'=>'Пользовательская колбэк-функция метабокса','Menu Icon'=>'Значок меню','The position in the sidebar menu in the admin dashboard.'=>'Позиция в меню боковой панели в панели администратора.','Menu Position'=>'Позиция меню','By default the post type will get a new top level item in the admin menu. If an existing top level item is supplied here, the post type will be added as a submenu item under it.'=>'По умолчанию тип записи является пунктом админ-меню верхнего уровня. Если указать здесь существующий пункт верхнего уровня, то тип записи будет добавлен в качестве его подпункта.','Admin Menu Parent'=>'Родительское меню администратора','Admin editor navigation in the sidebar menu.'=>'Навигация по редактору администратора в боковом меню.','Show In Admin Menu'=>'Показывать в меню админа','Items can be edited and managed in the admin dashboard.'=>'Управлять элементами и изменять их можно в консоли администратора.','Show In UI'=>'Показывать в интерфейсе','A link to a post.'=>'Ссылка на запись.','Description for a navigation link block variation.'=>'Описание для вариации блока навигационных ссылок.','Item Link Description'=>'Описание ссылки на элемент','A link to a %s.'=>'Ссылка на %s.','Post Link'=>'Ссылка записи','Title for a navigation link block variation.'=>'Заголовок для вариации блока навигационных ссылок.','Item Link'=>'Ссылка элемента','%s Link'=>'Cсылка на %s','Post updated.'=>'Запись обновлена.','In the editor notice after an item is updated.'=>'В уведомлении редактора после обновления элемента.','Item Updated'=>'Элемент обновлен','%s updated.'=>'%s обновлён.','Post scheduled.'=>'Запись запланирована к публикации.','In the editor notice after scheduling an item.'=>'В уведомлении редактора после планирования публикации элемента.','Item Scheduled'=>'Элемент запланирован','%s scheduled.'=>'%s запланировано.','Post reverted to draft.'=>'Запись возвращена в черновики.','In the editor notice after reverting an item to draft.'=>'В уведомлении редактора после возврата элемента в черновики.','Item Reverted To Draft'=>'Элемент возвращён к черновику','%s reverted to draft.'=>'%s преобразован в черновик.','Post published privately.'=>'Запись опубликована как личная.','In the editor notice after publishing a private item.'=>'В уведомлении редактора после публикации личного элемента.','Item Published Privately'=>'Элемент опубликован приватно','%s published privately.'=>'%s опубликована приватно.','Post published.'=>'Запись опубликована.','In the editor notice after publishing an item.'=>'В уведомлении редактора после публикации элемента.','Item Published'=>'Элемент опубликован','%s published.'=>'%s опубликовано.','Posts list'=>'Список записей','Used by screen readers for the items list on the post type list screen.'=>'Используется скринридерами для списка элементов на странице списка типа записей.','Items List'=>'Список элементов','%s list'=>'%s список','Posts list navigation'=>'Навигация по списку записей','Used by screen readers for the filter list pagination on the post type list screen.'=>'Используется скринридерами для заголовка фильтра пагинации на странице списка типа записей.','Items List Navigation'=>'Навигация по списку элементов','%s list navigation'=>'%s навигация по списку','Filter posts by date'=>'Фильтровать записи по дате','Used by screen readers for the filter by date heading on the post type list screen.'=>'Используется скринридерами для заголовка фильтра даты на странице списка типа записей.','Filter Items By Date'=>'Фильтровать элементы по дате','Filter %s by date'=>'Фильтр %s по дате','Filter posts list'=>'Фильтровать список записей','Used by screen readers for the filter links heading on the post type list screen.'=>'Используется скринридерами для фильтра заголовков ссылок на экране списка типа записей.','Filter Items List'=>'Фильтр списка элементов','Filter %s list'=>'Фильтровать список %s','In the media modal showing all media uploaded to this item.'=>'В модальном окне медиафайлов отображает все файлы, загруженные для этого элемента.','Uploaded To This Item'=>'Загружено в этот элемент','Uploaded to this %s'=>'Загружено в это %s','Insert into post'=>'Вставить в запись','As the button label when adding media to content.'=>'В качестве текста кнопки для добавлении медиафайлов в содержание.','Insert Into Media Button'=>'Кнопка вставки медиа','Insert into %s'=>'Вставить в %s','Use as featured image'=>'Использовать как изображение записи','As the button label for selecting to use an image as the featured image.'=>'В качестве текста кнопки для установки изображения в роли изображения записи.','Use Featured Image'=>'Использовать изображение записи','Remove featured image'=>'Удалить изображение записи','As the button label when removing the featured image.'=>'В качестве текста кнопки для удаления изображения записи.','Remove Featured Image'=>'Удалить изображение записи','Set featured image'=>'Задать изображение','As the button label when setting the featured image.'=>'В качестве текста кнопки для установки изображения записи.','Set Featured Image'=>'Задать изображение записи','Featured image'=>'Изображение записи','In the editor used for the title of the featured image meta box.'=>'Используется для заголовка метабокса изображения записи в редакторе.','Featured Image Meta Box'=>'Метабокс изображения записи','Post Attributes'=>'Свойства записи','In the editor used for the title of the post attributes meta box.'=>'Используется для заголовка метабокса свойств записи в редакторе.','Attributes Meta Box'=>'Метабокс свойств','%s Attributes'=>'Атрибуты %s','Post Archives'=>'Архивы записей','Adds \'Post Type Archive\' items with this label to the list of posts shown when adding items to an existing menu in a CPT with archives enabled. Only appears when editing menus in \'Live Preview\' mode and a custom archive slug has been provided.'=>'Добавляет элементы с меткой \'Архив элементов\' в список записей, отображаемый при добавлении элементов в существующее меню в CPT с включенными архивами. Появляется только при редактировании меню в режиме \'Live Preview\', если был задан пользовательский ярлык архива.','Archives Nav Menu'=>'Навигационное меню архивов','%s Archives'=>'Архивы %s','No posts found in Trash'=>'Записей в корзине не найдено','At the top of the post type list screen when there are no posts in the trash.'=>'В верхней части экрана списка типа записей, когда нет записей в корзине.','No Items Found in Trash'=>'Элементы не найдены в корзине','No %s found in Trash'=>'В корзине не найдено %s','No posts found'=>'Записей не найдено','At the top of the post type list screen when there are no posts to display.'=>'В верхней части экрана списка типа записей, когда нет записей для отображения.','No Items Found'=>'Элементов не найдено','No %s found'=>'Не найдено %s','Search Posts'=>'Поиск записей','At the top of the items screen when searching for an item.'=>'В верхней части экрана элементов при поиске элемента.','Search Items'=>'Поиск элементов','Search %s'=>'Поиск %s','Parent Page:'=>'Родительская страница:','For hierarchical types in the post type list screen.'=>'Для древовидных типов на экране со списком типов записей.','Parent Item Prefix'=>'Префикс родительского элемента','Parent %s:'=>'Родитель %s:','New Post'=>'Новая запись','New Item'=>'Новый элемент','New %s'=>'Новый %s','Add New Post'=>'Добавить запись','At the top of the editor screen when adding a new item.'=>'В верхней части экрана редактора при добавлении нового элемента.','Add New Item'=>'Добавить новый элемент','Add New %s'=>'Добавить новое %s','View Posts'=>'Просмотр записей','Appears in the admin bar in the \'All Posts\' view, provided the post type supports archives and the home page is not an archive of that post type.'=>'Отображается в панели администратора в представлении «Все записи», если тип записи поддерживает архивы и главная страница не является архивом этого типа записи.','View Items'=>'Просмотр элементов','View Post'=>'Просмотреть запись','In the admin bar to view item when editing it.'=>'В панели администратора для просмотра элемента при его редактировании.','View Item'=>'Просмотреть элемент','View %s'=>'Посмотреть %s','Edit Post'=>'Редактировать запись','At the top of the editor screen when editing an item.'=>'В верхней части экрана редактора при редактировании элемента.','Edit Item'=>'Изменить элемент','Edit %s'=>'Изменить %s','All Posts'=>'Все записи','In the post type submenu in the admin dashboard.'=>'В подменю типа записи на административной консоли.','All Items'=>'Все элементы','All %s'=>'Все %s','Admin menu name for the post type.'=>'Название в админ-меню для типа записей.','Menu Name'=>'Название меню','Regenerate all labels using the Singular and Plural labels'=>'Пересоздать все метки, используя метки единственного и множественного числа','Regenerate'=>'Регенерировать','Active post types are enabled and registered with WordPress.'=>'Активные типы записей включены и регистрируются в WordPress.','A descriptive summary of the post type.'=>'Описательная сводка типа поста.','Add Custom'=>'Добавить пользовательский','Enable various features in the content editor.'=>'Включить различные функции в редакторе содержимого.','Post Formats'=>'Форматы записей','Editor'=>'Редактор','Trackbacks'=>'Обратные ссылки','Select existing taxonomies to classify items of the post type.'=>'Выберите существующие таксономии, чтобы классифицировать элементы типа записи.','Browse Fields'=>'Выбрать поле','Nothing to import'=>'Импортировать нечего','. The Custom Post Type UI plugin can be deactivated.'=>'. Плагин Custom Post Type UI можно деактивировать.','Imported %d item from Custom Post Type UI -'=>'Импортировано %d запись из Custom Post Type UI -' . "\0" . 'Импортировано %d записи из Custom Post Type UI -' . "\0" . 'Импортировано %d записей из Custom Post Type UI -','Failed to import taxonomies.'=>'Не удалось импортировать таксономии.','Failed to import post types.'=>'Не удалось импортировать типы записи.','Nothing from Custom Post Type UI plugin selected for import.'=>'Ничего из плагина Custom Post Type UI не выбрано для импорта.','Imported 1 item'=>'Импортирован 1 элемент' . "\0" . 'Импортировано %s элемента' . "\0" . 'Импортировано %s элементов','Importing a Post Type or Taxonomy with the same key as one that already exists will overwrite the settings for the existing Post Type or Taxonomy with those of the import.'=>'Импорт типа записей или таксономии с уже существующим ключом приведет к перезаписи параметров существующего типа записей или таксономии параметрами из импорта.','Import from Custom Post Type UI'=>'Импорт из Custom Post Type UI','The following code can be used to register a local version of the selected items. Storing field groups, post types, or taxonomies locally can provide many benefits such as faster load times, version control & dynamic fields/settings. Simply copy and paste the following code to your theme\'s functions.php file or include it within an external file, then deactivate or delete the items from the ACF admin.'=>'Следующий код можно использовать для регистрации локальной версии выбранных элементов. Локальное хранение групп полей, типов записей или таксономий дает множество преимуществ, таких как ускоренная загрузка, контроль версий и динамические поля/настройки. Просто скопируйте и вставьте следующий код в файл functions.php вашей темы или включите его во внешний файл, а затем деактивируйте или удалите элементы из админки ACF.','Export - Generate PHP'=>'Экспорт - Генерация PHP','Export'=>'Экспорт','Select Taxonomies'=>'Выбрать таксономии','Select Post Types'=>'Выбрать типы записи','Exported 1 item.'=>'Экспортирован 1 элемент.' . "\0" . 'Экспортировано %s элемента.' . "\0" . 'Экспортировано %s элементов.','Category'=>'Рубрика','Tag'=>'Метка','%s taxonomy created'=>'Таксономия %s создана','%s taxonomy updated'=>'Таксономия %s обновлена','Taxonomy draft updated.'=>'Черновик таксономии обновлен.','Taxonomy scheduled for.'=>'Таксономия запланирована на.','Taxonomy submitted.'=>'Таксономия отправлена.','Taxonomy saved.'=>'Таксономия сохранена.','Taxonomy deleted.'=>'Таксономия удалена.','Taxonomy updated.'=>'Таксономия обновлена.','This taxonomy could not be registered because its key is in use by another taxonomy registered by another plugin or theme.'=>'Эта таксономия не может быть зарегистрирована, так как ее ключ используется другой таксономией, зарегистрированной другим плагином или темой.','Taxonomy synchronized.'=>'Таксономия синхронизирована' . "\0" . '%s таксономии синхронизированы' . "\0" . '%s таксономий синхронизировано','Taxonomy duplicated.'=>'Таксономия дублирована' . "\0" . '%s таксономии дублированы' . "\0" . '%s таксономий дублировано','Taxonomy deactivated.'=>'Таксономия деактивирована' . "\0" . '%s таксономии деактивированы' . "\0" . '%s таксономий деактивировано','Taxonomy activated.'=>'Таксономия активирована' . "\0" . '%s таксономии активированы' . "\0" . '%s таксономий активировано','Terms'=>'Термины','Post type synchronized.'=>'Тип записей синхронизирован' . "\0" . '%s типа записей синхронизированы' . "\0" . '%s типов записей синхронизировано','Post type duplicated.'=>'Тип записей дублирован' . "\0" . '%s типа записей дублированы' . "\0" . '%s типов записей дублировано','Post type deactivated.'=>'Тип записей деактивирован' . "\0" . '%s типа записей деактивированы' . "\0" . '%s типов записей деактивировано','Post type activated.'=>'Тип записей активирован' . "\0" . '%s тип записей активированы' . "\0" . '%s типов записей активировано','Post Types'=>'Типы записей','Advanced Settings'=>'Расширенные настройки','Basic Settings'=>'Базовые настройки','This post type could not be registered because its key is in use by another post type registered by another plugin or theme.'=>'Тип записей не может быть зарегистрирован, так как его ключ уже зарегистрирован другим плагином или темой.','Pages'=>'Страницы','Link Existing Field Groups'=>'Связать существующие группы полей','%s post type created'=>'Тип записи %s создан','Add fields to %s'=>'Добавить поля в %s','%s post type updated'=>'Тип записи %s обновлен','Post type draft updated.'=>'Черновик типа записей обновлен.','Post type scheduled for.'=>'Тип записей запланирован к публикации.','Post type submitted.'=>'Тип записей отправлен.','Post type saved.'=>'Тип записи сохранён.','Post type updated.'=>'Тип записей обновлен.','Post type deleted.'=>'Тип записей удален.','Type to search...'=>'Введите текст для поиска...','PRO Only'=>'Только для Про','Field groups linked successfully.'=>'Группы полей связаны успешно.','Import Post Types and Taxonomies registered with Custom Post Type UI and manage them with ACF. <a href="%s">Get Started</a>.'=>'Импортировать типы записей и таксономии, зарегистрированные через Custom Post Type UI, и управлять ими с помощью ACF. <a href="%s">Начать</a>.','ACF'=>'ACF','taxonomy'=>'таксономия','post type'=>'тип записи','Done'=>'Готово','Field Group(s)'=>'Группа(ы) полей','Select one or many field groups...'=>'Выберите одну или несколько групп полей...','Please select the field groups to link.'=>'Пожалуйста, выберете группы полей, чтобы связать.','Field group linked successfully.'=>'Группа полей связана успешно.' . "\0" . 'Группы полей связаны успешно.' . "\0" . 'Группы полей связаны успешно.','post statusRegistration Failed'=>'Регистрация не удалась','This item could not be registered because its key is in use by another item registered by another plugin or theme.'=>'Этот элемент не может быть зарегистрирован, так как его ключ используется другим элементом, зарегистрированным другим плагином или темой.','REST API'=>'Rest API','Permissions'=>'Разрешения','URLs'=>'URL-адреса','Visibility'=>'Видимость','Labels'=>'Этикетки','Field Settings Tabs'=>'Вкладки настроек полей','https://wpengine.com/?utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields'=>'https://wpengine.com/?utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields','[ACF shortcode value disabled for preview]'=>'[Значение шорткода ACF отключено для предварительного просмотра]','Close Modal'=>'Закрыть модальное окно','Field moved to other group'=>'Поле перемещено в другую группу','Close modal'=>'Закрыть модальное окно','Start a new group of tabs at this tab.'=>'Начать новую группу вкладок с этой вкладки.','New Tab Group'=>'Новая группа вкладок','Use a stylized checkbox using select2'=>'Используйте стилизованный флажок, используя select2','Save Other Choice'=>'Сохранить другой выбор','Allow Other Choice'=>'Разрешить другой выбор','Add Toggle All'=>'Добавить Переключить все','Save Custom Values'=>'Сохранить пользовательские значения','Allow Custom Values'=>'Разрешить пользовательские значения','Checkbox custom values cannot be empty. Uncheck any empty values.'=>'Пользовательские значения флажка не могут быть пустыми. Снимите флажки с пустых значений.','Updates'=>'Обновления','Advanced Custom Fields logo'=>'Логотип дополнительных настраиваемых полей','Save Changes'=>'Сохранить изменения','Field Group Title'=>'Название группы полей','Add title'=>'Добавить заголовок','New to ACF? Take a look at our <a href="%s" target="_blank">getting started guide</a>.'=>'Вы впервые в ACF? Ознакомьтесь с нашим <a href="%s" target="_blank">руководством по началу работы</a>.','Add Field Group'=>'Добавить группу полей','ACF uses <a href="%s" target="_blank">field groups</a> to group custom fields together, and then attach those fields to edit screens.'=>'ACF использует <a href="%s" target="_blank">группы полей</a> для группировки произвольных полей вместе, а затем присоединяет эти поля к экранным формам редактирования.','Add Your First Field Group'=>'Добавьте первую группу полей','Options Pages'=>'Страницы настроек','ACF Blocks'=>'Блоки ACF','Gallery Field'=>'Поле галереи','Flexible Content Field'=>'Гибкое поле содержимого','Repeater Field'=>'Повторяющееся поле','Unlock Extra Features with ACF PRO'=>'Разблокируйте дополнительные возможности с помощью ACF PRO','Delete Field Group'=>'Удалить группу полей','Created on %1$s at %2$s'=>'Создано %1$s в %2$s','Group Settings'=>'Настройки группы','Location Rules'=>'Правила местонахождения','Choose from over 30 field types. <a href="%s" target="_blank">Learn more</a>.'=>'Выбирайте из более чем 30 типов полей. <a href="%s" target="_blank">Подробнее</a>.','Get started creating new custom fields for your posts, pages, custom post types and other WordPress content.'=>'Начните создавать новые пользовательские поля для ваших записей, страниц, пользовательских типов записей и другого содержимого WordPress.','Add Your First Field'=>'Добавить первое поле','#'=>'#','Add Field'=>'Добавить поле','Presentation'=>'Презентация','Validation'=>'Валидация','General'=>'Общие','Import JSON'=>'Импорт JSON','Export As JSON'=>'Экспорт в формате JSON','Field group deactivated.'=>'%s группа полей деактивирована.' . "\0" . '%s группы полей деактивировано.' . "\0" . '%s групп полей деактивировано.','Field group activated.'=>'%s группа полей активирована.' . "\0" . '%s группы полей активировано.' . "\0" . '%s групп полей активировано.','Deactivate'=>'Деактивировать','Deactivate this item'=>'Деактивировать этот элемент','Activate'=>'Активировать','Activate this item'=>'Активировать этот элемент','Move field group to trash?'=>'Переместить группу полей в корзину?','post statusInactive'=>'Неактивна','WP Engine'=>'WP Engine','Advanced Custom Fields and Advanced Custom Fields PRO should not be active at the same time. We\'ve automatically deactivated Advanced Custom Fields PRO.'=>'Продвинутые пользовательские поля и Продвинутые пользовательские поля PRO не должны быть активны одновременно. Мы автоматически деактивировали Продвинутые пользовательские поля PRO.','Advanced Custom Fields and Advanced Custom Fields PRO should not be active at the same time. We\'ve automatically deactivated Advanced Custom Fields.'=>'Продвинутые пользовательские поля и Продвинутые пользовательские поля PRO не должны быть активны одновременно. Мы автоматически деактивировали Продвинутые пользовательские поля.','%1$s must have a user with the %2$s role.'=>'%1$s должно содержать пользователя со следующими ролью: %2$s' . "\0" . '%1$s должно содержать пользователя со следующими ролями: %2$s' . "\0" . '%1$s должно содержать пользователя со следующими ролями: %2$s','%1$s must have a valid user ID.'=>'%1$s должен иметь действительный ID пользователя.','Invalid request.'=>'Неверный запрос.','%1$s is not one of %2$s'=>'%1$s не является одним из %2$s','%1$s must have a valid post ID.'=>'%1$s должен иметь действительный ID записи.','%s requires a valid attachment ID.'=>'%s требуется действительный ID вложения.','Show in REST API'=>'Показывать в REST API','Enable Transparency'=>'Включить прозрачность','RGBA Array'=>'Массив RGBA','RGBA String'=>'Строка RGBA','Hex String'=>'Cтрока hex','Upgrade to PRO'=>'Обновить до PRO','post statusActive'=>'Активен','\'%s\' is not a valid email address'=>'«%s» не является корректным адресом электропочты','Color value'=>'Значение цвета','Select default color'=>'Выбрать стандартный цвет','Clear color'=>'Очистить цвет','Blocks'=>'Блоки','Options'=>'Настройки','Users'=>'Пользователи','Menu items'=>'Пункты меню','Widgets'=>'Виджеты','Attachments'=>'Вложения','Taxonomies'=>'Таксономии','Posts'=>'Записи','Last updated: %s'=>'Последнее изменение: %s','Sorry, this post is unavailable for diff comparison.'=>'Данная группа полей не доступна для сравнения отличий.','Invalid field group parameter(s).'=>'Неверный параметр(ы) группы полей.','Awaiting save'=>'Ожидает сохранения','Saved'=>'Сохранено','Import'=>'Импорт','Review changes'=>'Просмотр изменений','Located in: %s'=>'Находится в: %s','Located in plugin: %s'=>'Находится в плагине: %s','Located in theme: %s'=>'Находится в теме: %s','Various'=>'Различные','Sync changes'=>'Синхронизировать изменения','Loading diff'=>'Загрузка diff','Review local JSON changes'=>'Обзор локальных изменений JSON','Visit website'=>'Перейти на сайт','View details'=>'Подробности','Version %s'=>'Версия %s','Information'=>'Информация','<a href="%s" target="_blank">Help Desk</a>. The support professionals on our Help Desk will assist with your more in depth, technical challenges.'=>'<a href="%s" target="_blank">Служба поддержки</a>. Специалисты нашей службы поддержки помогут решить ваши технические проблемы.','<a href="%s" target="_blank">Discussions</a>. We have an active and friendly community on our Community Forums who may be able to help you figure out the \'how-tos\' of the ACF world.'=>'<a href="%s" target="_blank">Обсуждения</a>. У нас есть активное и дружелюбное сообщество на наших форумах сообщества, которое может помочь вам разобраться в практических приемах мира ACF.','<a href="%s" target="_blank">Documentation</a>. Our extensive documentation contains references and guides for most situations you may encounter.'=>'<a href="%s" target="_blank">Документация</a>. Наша подробная документация содержит ссылки и руководства для большинства ситуаций, с которыми вы можете столкнуться.','We are fanatical about support, and want you to get the best out of your website with ACF. If you run into any difficulties, there are several places you can find help:'=>'Мы фанатично относимся к поддержке и хотим, чтобы вы извлекали максимум из своего веб-сайта с помощью ACF. Если вы столкнетесь с какими-либо трудностями, есть несколько мест, где вы можете найти помощь:','Help & Support'=>'Помощь и поддержка','Please use the Help & Support tab to get in touch should you find yourself requiring assistance.'=>'Воспользуйтесь вкладкой «Справка и поддержка», чтобы связаться с нами, если вам потребуется помощь.','Before creating your first Field Group, we recommend first reading our <a href="%s" target="_blank">Getting started</a> guide to familiarize yourself with the plugin\'s philosophy and best practises.'=>'Перед созданием вашей первой группы полей мы рекомендуем сначала прочитать наше <a href="%s" target="_blank">руководство по началу работы</a>, чтобы ознакомиться с философией и передовыми практиками плагина.','The Advanced Custom Fields plugin provides a visual form builder to customize WordPress edit screens with extra fields, and an intuitive API to display custom field values in any theme template file.'=>'Плагин Advanced Custom Fields предоставляет визуальный конструктор форм для настройки экранов редактирования WordPress с дополнительными полями и интуитивно понятный API для отображения значений произвольных полей в любом файле шаблона темы.','Overview'=>'Обзор','Location type "%s" is already registered.'=>'Тип местоположения "%s" уже зарегистрирован.','Class "%s" does not exist.'=>'Класса "%s" не существует.','Invalid nonce.'=>'Неверный одноразовый номер.','Error loading field.'=>'Ошибка загрузки поля.','<strong>Error</strong>: %s'=>'<strong>Ошибка</strong>: %s','Widget'=>'Виджет','User Role'=>'Роль пользователя','Comment'=>'Комментарий','Post Format'=>'Формат записи','Menu Item'=>'Элемент меню','Post Status'=>'Статус записи','Menus'=>'Меню','Menu Locations'=>'Области для меню','Menu'=>'Меню','Post Taxonomy'=>'Таксономия записи','Child Page (has parent)'=>'Дочерняя страница (имеет родителя)','Parent Page (has children)'=>'Родительская страница (имеет дочерние)','Top Level Page (no parent)'=>'Страница верхнего уровня (без родителей)','Posts Page'=>'Страница записей','Front Page'=>'Главная страница','Page Type'=>'Тип страницы','Viewing back end'=>'Просмотр админки','Viewing front end'=>'Просмотр фронтэнда','Logged in'=>'Авторизован','Current User'=>'Текущий пользователь','Page Template'=>'Шаблон страницы','Register'=>'Регистрация','Add / Edit'=>'Добавить / изменить','User Form'=>'Форма пользователя','Page Parent'=>'Родительская страница','Super Admin'=>'Супер администратор','Current User Role'=>'Текущая роль пользователя','Default Template'=>'Шаблон по умолчанию','Post Template'=>'Шаблон записи','Post Category'=>'Рубрика записи','All %s formats'=>'Все %s форматы','Attachment'=>'Вложение','%s value is required'=>'%s значение требуется','Show this field if'=>'Показывать это поле, если','Conditional Logic'=>'Условная логика','and'=>'и','Local JSON'=>'Локальный JSON','Clone Field'=>'Клонировать поле','Please also check all premium add-ons (%s) are updated to the latest version.'=>'Также убедитесь, что все надстройки премиум-класса (%s) обновлены до последней версии.','This version contains improvements to your database and requires an upgrade.'=>'Эта версия содержит улучшения вашей базы данных и требует обновления.','Thank you for updating to %1$s v%2$s!'=>'Спасибо за обновление до %1$s v%2$s!','Database Upgrade Required'=>'Требуется обновление БД','Options Page'=>'Страница настроек','Gallery'=>'Галерея','Flexible Content'=>'Гибкое содержимое','Repeater'=>'Повторитель','Back to all tools'=>'Вернуться ко всем инструментам','If multiple field groups appear on an edit screen, the first field group\'s options will be used (the one with the lowest order number)'=>'Если на странице редактирования присутствует несколько групп полей, то будут использованы настройки первой из них (с наиболее низким значением порядка очередности)','<b>Select</b> items to <b>hide</b> them from the edit screen.'=>'<b>Выберите</b> блоки, которые необходимо <b>скрыть</b> на странице редактирования.','Hide on screen'=>'Скрыть на экране','Send Trackbacks'=>'Отправить обратные ссылки','Tags'=>'Метки','Categories'=>'Рубрики','Page Attributes'=>'Атрибуты страницы','Format'=>'Формат','Author'=>'Автор','Slug'=>'Ярлык','Revisions'=>'Редакции','Comments'=>'Комментарии','Discussion'=>'Обсуждение','Excerpt'=>'Отрывок','Content Editor'=>'Текстовый редактор','Permalink'=>'Постоянная ссылка','Shown in field group list'=>'Отображается в списке групп полей','Field groups with a lower order will appear first'=>'Группа полей с самым низким порядковым номером появится первой','Order No.'=>'Порядковый номер','Below fields'=>'Под полями','Below labels'=>'Под метками','Instruction Placement'=>'Размещение инструкции','Label Placement'=>'Размещение этикетки','Side'=>'На боковой панели','Normal (after content)'=>'Обычный (после содержимого)','High (after title)'=>'Высокий (после названия)','Position'=>'Позиция','Seamless (no metabox)'=>'Бесшовный (без метабокса)','Standard (WP metabox)'=>'Стандарт (метабокс WP)','Style'=>'Стиль','Type'=>'Тип','Key'=>'Ключ','Order'=>'Порядок','Close Field'=>'Закрыть поле','id'=>'id','class'=>'класс','width'=>'ширина','Wrapper Attributes'=>'Атрибуты обёртки','Required'=>'Обязательное','Instructions'=>'Инструкции','Field Type'=>'Тип поля','Single word, no spaces. Underscores and dashes allowed'=>'Одиночное слово, без пробелов. Подчеркивания и тире разрешены','Field Name'=>'Символьный код','This is the name which will appear on the EDIT page'=>'Имя поля на странице редактирования','Field Label'=>'Название поля','Delete'=>'Удалить','Delete field'=>'Удалить поле','Move'=>'Переместить','Move field to another group'=>'Переместить поле в другую группу','Duplicate field'=>'Дублировать поле','Edit field'=>'Изменить поле','Drag to reorder'=>'Перетащите, чтобы изменить порядок','Show this field group if'=>'Показать эту группу полей, если','No updates available.'=>'Обновлений нет.','Database upgrade complete. <a href="%s">See what\'s new</a>'=>'Обновление БД завершено. <a href="%s">Посмотрите, что нового</a>','Reading upgrade tasks...'=>'Чтения задач обновления...','Upgrade failed.'=>'Ошибка обновления.','Upgrade complete.'=>'Обновление завершено.','Upgrading data to version %s'=>'Обновление данных до версии %s','It is strongly recommended that you backup your database before proceeding. Are you sure you wish to run the updater now?'=>'Мы настоятельно рекомендуем сделать резервную копию базы данных перед началом работы. Вы уверены, что хотите запустить обновление сейчас?','Please select at least one site to upgrade.'=>'Выберите хотя бы один сайт для обновления.','Database Upgrade complete. <a href="%s">Return to network dashboard</a>'=>'Обновление БД закончено. <a href="%s">Вернуться в консоль</a>','Site is up to date'=>'Сайт обновлен','Site requires database upgrade from %1$s to %2$s'=>'Сайт требует обновления БД с %1$s до %2$s','Site'=>'Сайт','Upgrade Sites'=>'Обновление сайтов','The following sites require a DB upgrade. Check the ones you want to update and then click %s.'=>'Следующие сайты требуют обновления БД. Отметьте те, которые вы хотите обновить, а затем нажмите %s.','Add rule group'=>'Добавить группу правил','Create a set of rules to determine which edit screens will use these advanced custom fields'=>'Создайте набор правил для указания страниц, где следует отображать группу полей','Rules'=>'Правила','Copied'=>'Скопировано','Copy to clipboard'=>'Скопировать в буфер обмена','Select the items you would like to export and then select your export method. Export As JSON to export to a .json file which you can then import to another ACF installation. Generate PHP to export to PHP code which you can place in your theme.'=>'Выберите элементы, которые вы хотите экспортировать, а затем выберите метод экспорта. Экспортировать как JSON для экспорта в файл .json, который затем можно импортировать в другую установку ACF. Сгенерировать PHP для экспорта в PHP-код, который можно разместить в вашей теме.','Select Field Groups'=>'Выберите группы полей','No field groups selected'=>'Не выбраны группы полей','Generate PHP'=>'Генерировать PHP','Export Field Groups'=>'Экспорт групп полей','Import file empty'=>'Файл импорта пуст','Incorrect file type'=>'Неправильный тип файла','Error uploading file. Please try again'=>'Ошибка при загрузке файла. Попробуйте еще раз','Select the Advanced Custom Fields JSON file you would like to import. When you click the import button below, ACF will import the items in that file.'=>'Выберите файл ACF JSON, который вы хотите импортировать. Когда вы нажмете кнопку импорта ниже, ACF импортирует элементы из этого файла.','Import Field Groups'=>'Импорт групп полей','Sync'=>'Синхронизация','Select %s'=>'Выбрать %s','Duplicate'=>'Дублировать','Duplicate this item'=>'Дублировать элемент','Supports'=>'Поддержка','Documentation'=>'Документация','Description'=>'Описание','Sync available'=>'Доступна синхронизация','Field group synchronized.'=>'Группа полей синхронизирована.' . "\0" . '%s группы полей синхронизированы.' . "\0" . '%s групп полей синхронизированы.','Field group duplicated.'=>'% группа полей продублирована.' . "\0" . '%s группы полей продублировано.' . "\0" . '%s групп полей продублировано.','Active <span class="count">(%s)</span>'=>'Активна <span class="count">(%s)</span>' . "\0" . 'Активно <span class="count">(%s)</span>' . "\0" . 'Активны <span class="count">(%s)</span>','Review sites & upgrade'=>'Проверьте и обновите сайт','Upgrade Database'=>'Обновить базу данных','Custom Fields'=>'Произвольные поля','Move Field'=>'Переместить поле','Please select the destination for this field'=>'Выберите местоположение для этого поля','The %1$s field can now be found in the %2$s field group'=>'Поле %1$s теперь можно найти в группе полей %2$s','Move Complete.'=>'Движение завершено.','Active'=>'Активен','Field Keys'=>'Ключи полей','Settings'=>'Настройки','Location'=>'Местонахождение','Null'=>'Null','copy'=>'копировать','(this field)'=>'(текущее поле)','Checked'=>'Выбрано','Move Custom Field'=>'Переместить пользовательское поле','No toggle fields available'=>'Нет доступных переключаемых полей','Field group title is required'=>'Название группы полей обязательно','This field cannot be moved until its changes have been saved'=>'Это поле не может быть перемещено до сохранения изменений','The string "field_" may not be used at the start of a field name'=>'Строка "field_" не может использоваться в начале имени поля','Field group draft updated.'=>'Черновик группы полей обновлен.','Field group scheduled for.'=>'Группа полей запланирована на.','Field group submitted.'=>'Группа полей отправлена.','Field group saved.'=>'Группа полей сохранена.','Field group published.'=>'Группа полей опубликована.','Field group deleted.'=>'Группа полей удалена.','Field group updated.'=>'Группа полей обновлена.','Tools'=>'Инструменты','is not equal to'=>'не равно','is equal to'=>'равно','Forms'=>'Формы','Page'=>'Страница','Post'=>'Запись','Relational'=>'Отношение','Choice'=>'Выбор','Basic'=>'Базовый','Unknown'=>'Неизвестный','Field type does not exist'=>'Тип поля не существует','Spam Detected'=>'Обнаружение спама','Post updated'=>'Запись обновлена','Update'=>'Обновить','Validate Email'=>'Проверка Email','Content'=>'Содержимое','Title'=>'Заголовок','Edit field group'=>'Изменить группу полей','Selection is less than'=>'Отбор меньше, чем','Selection is greater than'=>'Отбор больше, чем','Value is less than'=>'Значение меньше чем','Value is greater than'=>'Значение больше чем','Value contains'=>'Значение содержит','Value matches pattern'=>'Значение соответствует паттерну','Value is not equal to'=>'Значение не равно','Value is equal to'=>'Значение равно','Has no value'=>'Не имеет значения','Has any value'=>'Имеет любое значение','Cancel'=>'Отмена','Are you sure?'=>'Вы уверены?','%d fields require attention'=>'%d полей требуют вашего внимания','1 field requires attention'=>'1 поле требует внимания','Validation failed'=>'Валидация не удалась','Validation successful'=>'Валидация пройдена успешно','Restricted'=>'Ограничено','Collapse Details'=>'Свернуть подробные сведения','Expand Details'=>'Развернуть подробные сведения','Uploaded to this post'=>'Загруженные для этой записи','verbUpdate'=>'Обновить','verbEdit'=>'Изменить','The changes you made will be lost if you navigate away from this page'=>'Внесенные вами изменения будут утеряны, если вы покинете эту страницу','File type must be %s.'=>'Тип файла должен быть %s.','or'=>'или','File size must not exceed %s.'=>'Размер файла не должен превышать %s.','File size must be at least %s.'=>'Размер файла должен быть не менее чем %s.','Image height must not exceed %dpx.'=>'Высота изображения не должна превышать %d px.','Image height must be at least %dpx.'=>'Высота изображения должна быть не менее %d px.','Image width must not exceed %dpx.'=>'Ширина изображения не должна превышать %d px.','Image width must be at least %dpx.'=>'Ширина изображения должна быть не менее %d px.','(no title)'=>'(без названия)','Full Size'=>'Полный','Large'=>'Большой','Medium'=>'Средний','Thumbnail'=>'Миниатюра','(no label)'=>'(без этикетки)','Sets the textarea height'=>'Задает высоту текстовой области','Rows'=>'Строки','Text Area'=>'Область текста','Prepend an extra checkbox to toggle all choices'=>'Добавьте дополнительный флажок, чтобы переключить все варианты','Save \'custom\' values to the field\'s choices'=>'Сохранить "пользовательские" значения для выбора поля','Allow \'custom\' values to be added'=>'Разрешить добавление «пользовательских» значений','Add new choice'=>'Добавить новый выбор','Toggle All'=>'Переключить все','Allow Archives URLs'=>'Разрешить URL-адреса архивов','Archives'=>'Архивы','Page Link'=>'Ссылка на страницу','Add'=>'Добавить','Name'=>'Имя','%s added'=>'%s добавлен','%s already exists'=>'%s уже существует','User unable to add new %s'=>'У пользователя нет возможности добавить новый %s','Term ID'=>'ID термина','Term Object'=>'Объект термина','Load value from posts terms'=>'Загрузить значения из терминов записей','Load Terms'=>'Загрузить термины','Connect selected terms to the post'=>'Связать выбранные термины с записью','Save Terms'=>'Сохранение терминов','Allow new terms to be created whilst editing'=>'Разрешить создание новых терминов во время редактирования','Create Terms'=>'Создание терминов','Radio Buttons'=>'Кнопки-переключатели','Single Value'=>'Одиночная значение','Multi Select'=>'Множественный выбор','Checkbox'=>'Чекбокс','Multiple Values'=>'Несколько значений','Select the appearance of this field'=>'Выберите способ отображения поля','Appearance'=>'Внешний вид','Select the taxonomy to be displayed'=>'Выберите таксономию для отображения','No TermsNo %s'=>'Нет %s','Value must be equal to or lower than %d'=>'Значение должно быть равным или меньшим чем %d','Value must be equal to or higher than %d'=>'Значение должно быть равным или больше чем %d','Value must be a number'=>'Значение должно быть числом','Number'=>'Число','Save \'other\' values to the field\'s choices'=>'Сохранить значения "другое" в выборы поля','Add \'other\' choice to allow for custom values'=>'Выберите значение "Другое", чтобы разрешить настраиваемые значения','Other'=>'Другое','Radio Button'=>'Кнопка-переключатель','Define an endpoint for the previous accordion to stop. This accordion will not be visible.'=>'Определяет конечную точку предыдущего аккордеона. Данный аккордеон будет невидим.','Allow this accordion to open without closing others.'=>'Позвольте этому аккордеону открываться, не закрывая другие.','Multi-Expand'=>'Многократное расширение','Display this accordion as open on page load.'=>'Отображать этот аккордеон как открытый при загрузке страницы.','Open'=>'Открыть','Accordion'=>'Аккордеон','Restrict which files can be uploaded'=>'Ограничить файлы, которые могут быть загружены','File ID'=>'ID файла','File URL'=>'URL файла','File Array'=>'Массив файлов','Add File'=>'Добавить файл','No file selected'=>'Файл не выбран','File name'=>'Имя файла','Update File'=>'Обновить файл','Edit File'=>'Изменить файл','Select File'=>'Выбрать файл','File'=>'Файл','Password'=>'Пароль','Specify the value returned'=>'Укажите возвращаемое значение','Use AJAX to lazy load choices?'=>'Использовать AJAX для отложенной загрузки вариантов?','Enter each default value on a new line'=>'Введите каждое значение по умолчанию с новой строки','verbSelect'=>'Выпадающий список','Select2 JS load_failLoading failed'=>'Загрузка не удалась','Select2 JS searchingSearching&hellip;'=>'Поиск&hellip;','Select2 JS load_moreLoading more results&hellip;'=>'Загрузить больше результатов&hellip;','Select2 JS selection_too_long_nYou can only select %d items'=>'Вы можете выбрать только %d элементов','Select2 JS selection_too_long_1You can only select 1 item'=>'Можно выбрать только 1 элемент','Select2 JS input_too_long_nPlease delete %d characters'=>'Удалите %d символов','Select2 JS input_too_long_1Please delete 1 character'=>'Удалите 1 символ','Select2 JS input_too_short_nPlease enter %d or more characters'=>'Введите %d или больше символов','Select2 JS input_too_short_1Please enter 1 or more characters'=>'Введите 1 или более символов','Select2 JS matches_0No matches found'=>'Соответствий не найдено','Select2 JS matches_n%d results are available, use up and down arrow keys to navigate.'=>'%d значений доступно, используйте клавиши вверх и вниз для навигации.','Select2 JS matches_1One result is available, press enter to select it.'=>'Доступен один результат, нажмите Enter, чтобы выбрать его.','nounSelect'=>'Выбрать','User ID'=>'ID пользователя','User Object'=>'Объект пользователя','User Array'=>'Массив пользователя','All user roles'=>'Все роли пользователей','Filter by Role'=>'Фильтровать по роли','User'=>'Пользователь','Separator'=>'Разделитель','Select Color'=>'Выбрать цвет','Default'=>'По умолчанию','Clear'=>'Сброс','Color Picker'=>'Цветовая палитра','Date Time Picker JS pmTextShortP'=>'P','Date Time Picker JS pmTextPM'=>'ПП','Date Time Picker JS amTextShortA'=>'A','Date Time Picker JS amTextAM'=>'ДП','Date Time Picker JS selectTextSelect'=>'Выбрать','Date Time Picker JS closeTextDone'=>'Готово','Date Time Picker JS currentTextNow'=>'Сейчас','Date Time Picker JS timezoneTextTime Zone'=>'Часовой пояс','Date Time Picker JS microsecTextMicrosecond'=>'Микросекунда','Date Time Picker JS millisecTextMillisecond'=>'Миллисекунда','Date Time Picker JS secondTextSecond'=>'Секунда','Date Time Picker JS minuteTextMinute'=>'Минута','Date Time Picker JS hourTextHour'=>'Час','Date Time Picker JS timeTextTime'=>'Время','Date Time Picker JS timeOnlyTitleChoose Time'=>'Выберите время','Date Time Picker'=>'Выбор даты и времени','Endpoint'=>'Конечная точка','Left aligned'=>'Выровнено по левому краю','Top aligned'=>'Выровнено по верхнему краю','Placement'=>'Расположение','Tab'=>'Вкладка','Value must be a valid URL'=>'Значение должно быть допустимым URL','Link URL'=>'URL ссылки','Link Array'=>'Массив ссылок','Opens in a new window/tab'=>'Откроется на новой вкладке','Select Link'=>'Выбрать ссылку','Link'=>'Ссылка','Email'=>'Email','Step Size'=>'Шаг изменения','Maximum Value'=>'Макс. значение','Minimum Value'=>'Минимальное значение','Range'=>'Диапазон','Both (Array)'=>'Оба (массив)','Label'=>'Этикетка','Value'=>'Значение','Vertical'=>'Вертикально','Horizontal'=>'Горизонтально','red : Red'=>'red : Красный','For more control, you may specify both a value and label like this:'=>'Для большего контроля вы можете указать и значение, и этикетку следующим образом:','Enter each choice on a new line.'=>'Введите каждый вариант с новой строки.','Choices'=>'Варианты','Button Group'=>'Группа кнопок','Allow Null'=>'Разрешить Null','Parent'=>'Родитель','TinyMCE will not be initialized until field is clicked'=>'TinyMCE не будет инициализирован, пока не будет нажато поле','Delay Initialization'=>'Задержка инициализации','Show Media Upload Buttons'=>'Показать кнопки загрузки медиа файлов','Toolbar'=>'Верхняя панель','Text Only'=>'Только текст','Visual Only'=>'Только визуально','Visual & Text'=>'Визуально и текст','Tabs'=>'Вкладки','Click to initialize TinyMCE'=>'Нажмите для инициализации TinyMCE','Name for the Text editor tab (formerly HTML)Text'=>'Текст','Visual'=>'Визуально','Value must not exceed %d characters'=>'Значение не должно превышать %d символов','Leave blank for no limit'=>'Оставьте пустым, чтобы не ограничивать','Character Limit'=>'Ограничение кол-ва символов','Appears after the input'=>'Появляется после ввода','Append'=>'Добавить','Appears before the input'=>'Появляется перед вводом','Prepend'=>'Добавить в начало','Appears within the input'=>'Появляется перед полем ввода','Placeholder Text'=>'Текст-заполнитель','Appears when creating a new post'=>'Появляется при создании новой записи','Text'=>'Текст','%1$s requires at least %2$s selection'=>'%1$s требует выбора как минимум %2$s элемента' . "\0" . '%1$s требует выбора как минимум %2$s элемента' . "\0" . '%1$s требует выбора как минимум %2$s элементов','Post ID'=>'ID записи','Post Object'=>'Объект записи','Maximum Posts'=>'Макс. кол-во записей','Minimum Posts'=>'Мин. кол-во записей','Featured Image'=>'Изображение записи','Selected elements will be displayed in each result'=>'Выбранные элементы будут отображены в каждом результате','Elements'=>'Элементы','Taxonomy'=>'Таксономия','Post Type'=>'Тип записи','Filters'=>'Фильтры','All taxonomies'=>'Все таксономии','Filter by Taxonomy'=>'Фильтрация по таксономии','All post types'=>'Все типы записи','Filter by Post Type'=>'Фильтрация по типу записей','Search...'=>'Поиск...','Select taxonomy'=>'Выбрать таксономию','Select post type'=>'Выбрать тип записи','No matches found'=>'Соответствий не найдено','Loading'=>'Загрузка','Maximum values reached ( {max} values )'=>'Достигнуты макс. значения ( {max} values )','Relationship'=>'Родственные связи','Comma separated list. Leave blank for all types'=>'Для разделения типов файлов используйте запятые. Оставьте поле пустым для разрешения загрузки всех файлов','Allowed File Types'=>'Разрешенные типы файлов','Maximum'=>'Максимум','File size'=>'Размер файла','Restrict which images can be uploaded'=>'Ограничить изображения, которые могут быть загружены','Minimum'=>'Минимум','Uploaded to post'=>'Загружено в запись','All'=>'Все','Limit the media library choice'=>'Ограничить выбор из библиотеки файлов','Library'=>'Библиотека','Preview Size'=>'Размер предпросмотра','Image ID'=>'ID изображения','Image URL'=>'URL изображения','Image Array'=>'Массив изображения','Specify the returned value on front end'=>'Укажите возвращаемое значение на фронтедне','Return Value'=>'Возвращаемое значение','Add Image'=>'Добавить изображение','No image selected'=>'Изображение не выбрано','Remove'=>'Удалить','Edit'=>'Изменить','All images'=>'Все изображения','Update Image'=>'Обновить изображение','Edit Image'=>'Редактировать','Select Image'=>'Выбрать изображение','Image'=>'Изображение','Allow HTML markup to display as visible text instead of rendering'=>'Разрешить HTML-разметке отображаться в виде видимого текста вместо отрисовки','Escape HTML'=>'Escape HTML','No Formatting'=>'Без форматирования','Automatically add &lt;br&gt;'=>'Автоматически добавлять &lt;br&gt;','Automatically add paragraphs'=>'Автоматически добавлять абзацы','Controls how new lines are rendered'=>'Управляет отрисовкой новых линий','New Lines'=>'Новые строки','Week Starts On'=>'Неделя начинается с','The format used when saving a value'=>'Формат, используемый при сохранении значения','Save Format'=>'Сохранить формат','Date Picker JS weekHeaderWk'=>'Нед.','Date Picker JS prevTextPrev'=>'Назад','Date Picker JS nextTextNext'=>'Далее','Date Picker JS currentTextToday'=>'Сегодня','Date Picker JS closeTextDone'=>'Готово','Date Picker'=>'Выбор даты','Width'=>'Ширина','Embed Size'=>'Размер встраивания','Enter URL'=>'Введите URL','oEmbed'=>'oEmbed','Text shown when inactive'=>'Текст отображается, когда он неактивен','Off Text'=>'Отключить текст','Text shown when active'=>'Текст, отображаемый при активности','On Text'=>'На тексте','Stylized UI'=>'Стилизованный интерфейс','Default Value'=>'Значение по умолчанию','Displays text alongside the checkbox'=>'Отображать текст рядом с флажком','Message'=>'Сообщение','No'=>'Нет','Yes'=>'Да','True / False'=>'True / False','Row'=>'Строка','Table'=>'Таблица','Block'=>'Блок','Specify the style used to render the selected fields'=>'Укажите стиль, используемый для отрисовки выбранных полей','Layout'=>'Макет','Sub Fields'=>'Вложенные поля','Group'=>'Группа','Customize the map height'=>'Настройка высоты карты','Height'=>'Высота','Set the initial zoom level'=>'Укажите начальный масштаб','Zoom'=>'Увеличить','Center the initial map'=>'Центрировать начальную карту','Center'=>'По центру','Search for address...'=>'Поиск по адресу...','Find current location'=>'Определить текущее местоположение','Clear location'=>'Очистить местоположение','Search'=>'Поиск','Sorry, this browser does not support geolocation'=>'Ваш браузер не поддерживает определение местоположения','Google Map'=>'Google Карта','The format returned via template functions'=>'Формат, возвращаемый через функции шаблона','Return Format'=>'Формат возврата','Custom:'=>'Пользовательский:','The format displayed when editing a post'=>'Формат, отображаемый при редактировании записи','Display Format'=>'Отображаемый формат','Time Picker'=>'Подборщик времени','Inactive <span class="count">(%s)</span>'=>'Неактивен <span class="count">(%s)</span>' . "\0" . 'Неактивны <span class="count">(%s)</span>' . "\0" . 'Неактивно <span class="count">(%s)</span>','No Fields found in Trash'=>'Поля не найдены в корзине','No Fields found'=>'Поля не найдены','Search Fields'=>'Поиск полей','View Field'=>'Просмотреть поле','New Field'=>'Новое поле','Edit Field'=>'Изменить поле','Add New Field'=>'Добавить новое поле','Field'=>'Поле','Fields'=>'Поля','No Field Groups found in Trash'=>'Группы полей не найдены в корзине','No Field Groups found'=>'Группы полей не найдены','Search Field Groups'=>'Найти группу полей','View Field Group'=>'Просмотреть группу полей','New Field Group'=>'Новая группа полей','Edit Field Group'=>'Редактирование группы полей','Add New Field Group'=>'Добавить новую группу полей','Add New'=>'Добавить новое','Field Group'=>'Группа полей','Field Groups'=>'Группы полей','Customize WordPress with powerful, professional and intuitive fields.'=>'Настройте WordPress с помощью мощных, профессиональных и интуитивно понятных полей.','https://www.advancedcustomfields.com'=>'https://www.advancedcustomfields.com','Advanced Custom Fields'=>'Advanced Custom Fields','Advanced Custom Fields PRO'=>'Advanced Custom Fields PRO','Block type name is required.'=>'%s значение требуется','%s settings'=>'Настройки','Options Updated'=>'Настройки были обновлены','To enable updates, please enter your license key on the <a href="%1$s">Updates</a> page. If you don\'t have a licence key, please see <a href="%2$s" target="_blank">details & pricing</a>.'=>'Для разблокировки обновлений введите ваш лицензионный ключ на странице <a href="%s">Обновление</a>. Если у вас его нет, то ознакомьтесь с <a href="%s" target="_blank">деталями</a>.','<b>ACF Activation Error</b>. An error occurred when connecting to activation server'=>'<b>Ошибка</b>. Не удалось подключиться к серверу обновлений','Check Again'=>'Проверить еще раз','<b>ACF Activation Error</b>. Could not connect to activation server'=>'<b>Ошибка</b>. Не удалось подключиться к серверу обновлений','Publish'=>'Опубликовано','No Custom Field Groups found for this options page. <a href="%s">Create a Custom Field Group</a>'=>'С этой страницей настроек не связаны группы полей. <a href="%s">Создать группу полей</a>','<b>Error</b>. Could not connect to update server'=>'<b>Ошибка</b>. Не удалось подключиться к серверу обновлений','<b>Error</b>. Your license for this site has expired or been deactivated. Please reactivate your ACF PRO license.'=>'Во время проверки лицензии, которая связана с адресом сайта, возникла ошибка. Пожалуйста, выполните активацию снова','Select one or more fields you wish to clone'=>'Выберите одно или несколько полей, которые вы хотите клонировать','Display'=>'Способ отображения','Specify the style used to render the clone field'=>'Выберите стиль отображения клонированных полей','Group (displays selected fields in a group within this field)'=>'Группа (сгруппировать выбранные поля в одно и выводить вместо текущего)','Seamless (replaces this field with selected fields)'=>'Отдельно (выбранные поля выводятся отдельно вместо текущего)','Labels will be displayed as %s'=>'Ярлыки будут отображаться как %s','Prefix Field Labels'=>'Префикс для ярлыков полей','Values will be saved as %s'=>'Значения будут сохранены как %s','Prefix Field Names'=>'Префикс для названий полей','Unknown field'=>'Неизвестное поле','Unknown field group'=>'Неизвестная группа полей','All fields from %s field group'=>'Все поля группы %s','Add Row'=>'Добавить','layout'=>'макет' . "\0" . 'макета' . "\0" . 'макетов','layouts'=>'макеты','This field requires at least {min} {label} {identifier}'=>'Это поле требует как минимум {min} {label}  {identifier}','This field has a limit of {max} {label} {identifier}'=>'Это поле ограничено {max} {label} {identifier}','{available} {label} {identifier} available (max {max})'=>'{available} {label} {identifier} доступно (максимум {max})','{required} {label} {identifier} required (min {min})'=>'{required} {label} {identifier} требуется (минимум {min})','Flexible Content requires at least 1 layout'=>'Для гибкого содержания требуется как минимум один макет','Click the "%s" button below to start creating your layout'=>'Нажмите на кнопку "%s" ниже для начала создания собственного макета','Add layout'=>'Добавить макет','Duplicate layout'=>'Дублировать макет','Remove layout'=>'Удалить макет','Click to toggle'=>'Нажмите для переключения','Delete Layout'=>'Удалить макет','Duplicate Layout'=>'Дублировать макет','Add New Layout'=>'Добавить новый макет','Add Layout'=>'Добавить макет','Min'=>'Минимум','Max'=>'Максимум','Minimum Layouts'=>'Мин. количество блоков','Maximum Layouts'=>'Макс. количество блоков','Button Label'=>'Текст кнопки добавления','Add Image to Gallery'=>'Добавление изображений в галерею','Maximum selection reached'=>'Выбрано максимальное количество изображений','Length'=>'Длина','Caption'=>'Подпись','Alt Text'=>'Текст в ALT','Add to gallery'=>'Добавить изображения','Bulk actions'=>'Сортировка','Sort by date uploaded'=>'По дате загрузки','Sort by date modified'=>'По дате изменения','Sort by title'=>'По названию','Reverse current order'=>'Инвертировать','Close'=>'Закрыть','Minimum Selection'=>'Мин. количество изображений','Maximum Selection'=>'Макс. количество изображений','Allowed file types'=>'Допустимые типы файлов','Insert'=>'Добавить','Specify where new attachments are added'=>'Укажите куда добавлять новые вложения','Append to the end'=>'Добавлять в конец','Prepend to the beginning'=>'Добавлять в начало','Minimum rows not reached ({min} rows)'=>'Достигнуто минимальное количество ({min} элементов)','Maximum rows reached ({max} rows)'=>'Достигнуто максимальное количество ({max} элементов)','Error loading page'=>'Возникла ошибка при загрузке обновления','Rows Per Page'=>'Страница записей','Set the number of rows to be displayed on a page.'=>'Выберите таксономию для отображения','Minimum Rows'=>'Мин. количество элементов','Maximum Rows'=>'Макс. количество элементов','Collapsed'=>'Сокращенный заголовок','Select a sub field to show when row is collapsed'=>'Выберите поле, которое будет отображаться в качестве заголовка при сворачивании блока','Click to reorder'=>'Потяните для изменения порядка','Add row'=>'Добавить','Duplicate row'=>'Дублировать','Remove row'=>'Удалить','Current Page'=>'Текущий пользователь','First Page'=>'Главная страница','Previous Page'=>'Страница записей','Next Page'=>'Главная страница','Last Page'=>'Страница записей','No block types exist'=>'Страницы с настройками отсуствуют','No options pages exist'=>'Страницы с настройками отсуствуют','Deactivate License'=>'Деактивировать лицензию','Activate License'=>'Активировать лицензию','License Information'=>'Информация о лицензии','To unlock updates, please enter your license key below. If you don\'t have a licence key, please see <a href="%s" target="_blank">details & pricing</a>.'=>'Для разблокирования обновлений введите лицензионный ключ ниже. Если у вас его нет, то ознакомьтесь с <a href="%s" target="_blank">деталями</a>.','License Key'=>'Номер лицензии','Retry Activation'=>'Код активации','Update Information'=>'Обновления','Current Version'=>'Текущая версия','Latest Version'=>'Последняя версия','Update Available'=>'Обновления доступны','Upgrade Notice'=>'Замечания по обновлению','Enter your license key to unlock updates'=>'Пожалуйста введите ваш номер лицензии для разблокировки обновлений','Update Plugin'=>'Обновить плагин','Please reactivate your license to unlock updates'=>'Пожалуйста введите ваш номер лицензии для разблокировки обновлений']];
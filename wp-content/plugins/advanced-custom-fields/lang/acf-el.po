# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-05-19T16:45:13+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: el\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/fields/class-acf-field-select.php:471
msgid ""
"Save created options back to the \"Choices\" setting in the field definition."
msgstr ""

#: includes/fields/class-acf-field-select.php:470
msgid "Save Options"
msgstr ""

#: includes/fields/class-acf-field-select.php:448
msgid ""
"Allow content editors to create new options by typing in the Select input. "
"Multiple options can be created from a comma separated string."
msgstr ""

#: includes/fields/class-acf-field-select.php:447
msgid "Create Options"
msgstr ""

#: includes/admin/views/global/navigation.php:179
#: includes/admin/views/global/navigation.php:183
msgid "Edit ACF Field Groups"
msgstr ""

#: includes/admin/views/global/navigation.php:100
msgid "Get 4 months free on any WP Engine plan"
msgstr ""

#: src/Site_Health/Site_Health.php:522
msgid "Number of Field Groups with Blocks and Other Locations"
msgstr ""

#: src/Site_Health/Site_Health.php:517
msgid "Number of Field Groups with Multiple Block Locations"
msgstr ""

#: src/Site_Health/Site_Health.php:512
msgid "Number of Field Groups with a Single Block Location"
msgstr ""

#: src/Site_Health/Site_Health.php:481
msgid "All Location Rules"
msgstr ""

#: includes/validation.php:144
msgid "Learn more"
msgstr ""

#: includes/validation.php:133
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr ""

#: includes/validation.php:131
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr ""

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:324
msgid "are developed and maintained by"
msgstr ""

#: src/Site_Health/Site_Health.php:295
msgid "Update Source"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr ""

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""

#: src/Site_Health/Site_Health.php:296
msgid "wordpress.org"
msgstr ""

#: includes/fields/class-acf-field.php:359
msgid "Allow Access to Value in Editor UI"
msgstr ""

#: includes/fields/class-acf-field.php:341
msgid "Learn more."
msgstr ""

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:340
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""

#: src/Blocks/Bindings.php:67
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""

#: includes/api/api-template.php:1085 src/Blocks/Bindings.php:75
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr ""

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr ""

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Businessman Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Forums Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:747
msgid "YouTube Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:746
msgid "Yes (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:744
msgid "Xing Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:743
msgid "WordPress (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:741
msgid "WhatsApp Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:740
msgid "Write Blog Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:739
msgid "Widgets Menus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:738
msgid "View Site Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:737
msgid "Learn More Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:735
msgid "Add Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:732
msgid "Video (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:731
msgid "Video (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:730
msgid "Video (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:727
msgid "Update (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:724
msgid "Universal Access (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Twitter (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Twitch Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "Tide Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Tickets (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Text Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Table Row Delete Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Table Row Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Table Row After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Table Col Delete Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Table Col Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Table Col After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Superhero (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Superhero Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Spotify Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Shortcode Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Shield (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "Share (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "Share (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Saved Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "RSS Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "REST API Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Remove Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Reddit Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Privacy Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Printer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Podio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Plus (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Plus (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "Plugins Checked Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Pinterest Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "Pets Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "PDF Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "Palm Tree Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "Open Folder Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "No (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Money (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Menu (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Menu (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Menu (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Spreadsheet Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Interactive Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Document Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Default Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "Location (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "LinkedIn Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Instagram Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Insert Before Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Insert After Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Insert Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Info Outline Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:608
msgid "Images (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Images (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Rotate Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Rotate Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Rotate Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Flip Vertical Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Flip Horizontal Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Crop Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "ID (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "HTML Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "Hourglass Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Heading Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Google Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Games Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Fullscreen Exit (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Fullscreen (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Status Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Image Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Gallery Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Chat Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:578
#: includes/fields/class-acf-field-icon_picker.php:627
msgid "Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Aside Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Food Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Exit Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Excerpt View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Embed Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Embed Post Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Embed Photo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Embed Generic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Embed Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Email (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:559
msgid "Ellipsis Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Unordered List Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "RTL Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Ordered List RTL Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Ordered List Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "LTR Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:533
msgid "Custom Character Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "Edit Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Edit Large Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Drumstick Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Database View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Database Remove Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "Database Import Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Database Export Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Database Add Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Database Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Cover Image Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Volume On Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Volume Off Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Skip Forward Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Skip Back Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:506
msgid "Repeat Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Play Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Pause Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Forward Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Back Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Columns Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Color Picker Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Coffee Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Code Standards Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Cloud Upload Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Cloud Saved Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Car Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Camera (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Calculator Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Button Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Businessperson Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Tracking Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Topics Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Replies Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "PM Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Friends Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Community Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "BuddyPress Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "bbPress Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Activity Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Book (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Block Default Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Bell Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Beer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Bank Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Arrow Up (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Arrow Up (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Arrow Right (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Arrow Right (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Arrow Left (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Arrow Left (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:445
msgid "Arrow Down (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Arrow Down (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "Amazon Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Align Wide Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Align Pull Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Align Pull Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Align Full Width Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Airplane Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Site (alt3) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Site (alt2) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Site (alt) Icon"
msgstr ""

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr ""

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr ""

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr ""

#: src/Site_Health/Site_Health.php:720
msgid "Blocks Using Post Meta"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr ""

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:813
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr ""

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:797
msgid "%s is a required property of acf."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:773
msgid "The value of icon to save."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:767
msgid "The type of icon to save."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:745
msgid "Yes Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "WordPress Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:734
msgid "Warning Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:733
msgid "Visibility Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:729
msgid "Vault Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:728
msgid "Upload Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:726
msgid "Update Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:725
msgid "Unlock Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:723
msgid "Universal Access Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "Undo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Twitter Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "Trash Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:717
msgid "Translation Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Tickets Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "Thumbs Up Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Thumbs Down Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:633
#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Text Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Testimonial Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Tagcloud Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Tag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Tablet Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Store Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Sticky Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Star Half Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Star Filled Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Star Empty Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Sos Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Sort Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Smiley Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Smartphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Slides Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Shield Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Share Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Search Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Screen Options Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Schedule Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Redo Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Randomize Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Products Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Pressthis Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Post Status Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Portfolio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Plus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Playlist Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Playlist Audio Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "Phone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Performance Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Paperclip Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "No Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Networking Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Nametag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Move Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Money Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Minus Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Migrate Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:640
msgid "Microphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Megaphone Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Marker Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "Lock Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Location Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "List View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Lightbulb Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Left Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Layout Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Laptop Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Info Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Index Card Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "ID Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "Hidden Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Heart Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Hammer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:470
#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Groups Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Grid View Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Forms Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Flag Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:574
#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Filter Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Feedback Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "Facebook (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Facebook Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "External Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Email (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Email Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:558
#: includes/fields/class-acf-field-icon_picker.php:584
#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Video Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Unlink Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Underline Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Text Color Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:553
msgid "Table Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Strikethrough Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Spellcheck Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:549
msgid "Remove Formatting Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:548
#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Quote Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Paste Word Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Paste Text Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "Paragraph Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Outdent Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Kitchen Sink Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Justify Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Italic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Insert More Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Indent Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Help Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Expand Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Contract Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:531
#: includes/fields/class-acf-field-icon_picker.php:628
msgid "Code Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Break Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Bold Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:523
msgid "Edit Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Download Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Dismiss Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Desktop Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Dashboard Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Cloud Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Clock Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Clipboard Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Chart Pie Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Chart Line Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Chart Bar Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Chart Area Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Category Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Cart Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Carrot Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Camera Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Calendar (alt) Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Calendar Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Businesswoman Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Building Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Book Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Backup Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Awards Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Art Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Arrow Up Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Arrow Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Arrow Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Arrow Down Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:442
#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Archive Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "Analytics Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:438
#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Align Right Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Align None Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:434
#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Align Left Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:432
#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Align Center Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Album Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Users Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Tools Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Site Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Settings Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Post Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Plugins Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Page Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Network Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Multisite Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:417
msgid "Media Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Links Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Home Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:413
msgid "Customizer Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:412
#: includes/fields/class-acf-field-icon_picker.php:736
msgid "Comments Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Collapse Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Appearance Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Generic Icon"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:346
msgid "Icon picker requires a value."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:341
msgid "Icon picker requires an icon type."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:310
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:309
msgid "No results found for that search term"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:291
msgid "Array"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:290
msgid "String"
msgstr ""

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:278
msgid "Specify the return format for the icon. %s"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:263
msgid "Select where content editors can choose the icon from."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:224
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:207
msgid "Browse Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:198
msgid "The currently selected image preview"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:189
msgid "Click to change the icon in the Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:84
msgid "Search icons..."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr ""

#: src/Site_Health/Site_Health.php:781
msgid "JSON Load Paths"
msgstr ""

#: src/Site_Health/Site_Health.php:775
msgid "JSON Save Paths"
msgstr ""

#: src/Site_Health/Site_Health.php:766
msgid "Registered ACF Forms"
msgstr ""

#: src/Site_Health/Site_Health.php:760
msgid "Shortcode Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:752
msgid "Field Settings Tabs Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:744
msgid "Field Type Modal Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:736
msgid "Admin UI Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:727
msgid "Block Preloading Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:715
msgid "Blocks Per ACF Block Version"
msgstr ""

#: src/Site_Health/Site_Health.php:710
msgid "Blocks Per API Version"
msgstr ""

#: src/Site_Health/Site_Health.php:683
msgid "Registered ACF Blocks"
msgstr ""

#: src/Site_Health/Site_Health.php:677
msgid "Light"
msgstr ""

#: src/Site_Health/Site_Health.php:677
msgid "Standard"
msgstr ""

#: src/Site_Health/Site_Health.php:676
msgid "REST API Format"
msgstr ""

#: src/Site_Health/Site_Health.php:668
msgid "Registered Options Pages (PHP)"
msgstr ""

#: src/Site_Health/Site_Health.php:654
msgid "Registered Options Pages (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:649
msgid "Registered Options Pages (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:619
msgid "Options Pages UI Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:611
msgid "Registered Taxonomies (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:599
msgid "Registered Taxonomies (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:587
msgid "Registered Post Types (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:575
msgid "Registered Post Types (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:562
msgid "Post Types and Taxonomies Enabled"
msgstr ""

#: src/Site_Health/Site_Health.php:555
msgid "Number of Third Party Fields by Field Type"
msgstr ""

#: src/Site_Health/Site_Health.php:550
msgid "Number of Fields by Field Type"
msgstr ""

#: src/Site_Health/Site_Health.php:449
msgid "Field Groups Enabled for GraphQL"
msgstr ""

#: src/Site_Health/Site_Health.php:436
msgid "Field Groups Enabled for REST API"
msgstr ""

#: src/Site_Health/Site_Health.php:424
msgid "Registered Field Groups (JSON)"
msgstr ""

#: src/Site_Health/Site_Health.php:412
msgid "Registered Field Groups (PHP)"
msgstr ""

#: src/Site_Health/Site_Health.php:400
msgid "Registered Field Groups (UI)"
msgstr ""

#: src/Site_Health/Site_Health.php:388
msgid "Active Plugins"
msgstr ""

#: src/Site_Health/Site_Health.php:362
msgid "Parent Theme"
msgstr ""

#: src/Site_Health/Site_Health.php:351
msgid "Active Theme"
msgstr ""

#: src/Site_Health/Site_Health.php:342
msgid "Is Multisite"
msgstr ""

#: src/Site_Health/Site_Health.php:337
msgid "MySQL Version"
msgstr ""

#: src/Site_Health/Site_Health.php:332
msgid "WordPress Version"
msgstr ""

#: src/Site_Health/Site_Health.php:325
msgid "Subscription Expiry Date"
msgstr ""

#: src/Site_Health/Site_Health.php:317
msgid "License Status"
msgstr ""

#: src/Site_Health/Site_Health.php:312
msgid "License Type"
msgstr ""

#: src/Site_Health/Site_Health.php:307
msgid "Licensed URL"
msgstr ""

#: src/Site_Health/Site_Health.php:301
msgid "License Activated"
msgstr ""

#: src/Site_Health/Site_Health.php:290
msgid "Free"
msgstr ""

#: src/Site_Health/Site_Health.php:289
msgid "Plugin Type"
msgstr ""

#: src/Site_Health/Site_Health.php:284
msgid "Plugin Version"
msgstr ""

#: src/Site_Health/Site_Health.php:255
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr ""

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:143
msgid "Has no term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has any term selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:141
msgid "Terms do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:139
msgid "Term is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:137
msgid "Has no user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has any user selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:135
msgid "Users do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:133
msgid "User is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:131
msgid "Has no page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has any page selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:129
msgid "Pages do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:127
msgid "Page is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:125
msgid "Has no relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has any relationship selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has no post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has any post selected"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:121
msgid "Posts do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:119
msgid "Post is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:117
msgid "Relationships do not contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships contain"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationship is not equal to"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is equal to"
msgstr ""

#: src/Blocks/Bindings.php:38
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr ""

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr ""

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr ""

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr ""

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr ""

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr ""

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr ""

#: includes/admin/views/global/navigation.php:229
msgid "Renew ACF PRO License"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr ""

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr ""

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr ""

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr ""

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:147
msgid "This Field"
msgstr ""

#: includes/admin/admin.php:361
msgid "ACF PRO"
msgstr ""

#: includes/admin/admin.php:359
msgid "Feedback"
msgstr ""

#: includes/admin/admin.php:357
msgid "Support"
msgstr ""

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:332
msgid "is developed and maintained by"
msgstr ""

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr ""

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr ""

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr ""

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr ""

#: includes/admin/views/global/navigation.php:241
msgid "WP Engine logo"
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr ""

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:273
msgid "Learn More"
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr ""

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr ""

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr ""

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr ""

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr ""

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr ""

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr ""

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr ""

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr ""

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr ""

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr ""

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr ""

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""

#: includes/fields/class-acf-field-select.php:18
msgid "A dropdown list with a selection of choices that you specify."
msgstr ""

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr ""

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr ""

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr ""

#: includes/admin/views/global/navigation.php:86 includes/fields.php:331
#: src/Site_Health/Site_Health.php:290
msgid "PRO"
msgstr ""

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr ""

#: includes/admin/views/global/navigation.php:192
msgid "More"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr ""

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr ""

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:97
msgid "No search results for '%s'"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr ""

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr ""

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr ""

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr ""

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr ""

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr ""

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr ""

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr ""

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Menu Icon"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr ""

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr ""

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr ""

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr ""

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr ""

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr ""

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr ""

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ""

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr ""

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr ""

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr ""

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr ""

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr ""

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr ""

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr ""

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr ""

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr ""

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:146
msgid "Type to search..."
msgstr ""

#: includes/admin/post-types/admin-field-group.php:101
msgid "PRO Only"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:93
msgid "Field groups linked successfully."
msgstr ""

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""

#: includes/admin/admin.php:46 includes/admin/admin.php:361
#: src/Site_Health/Site_Health.php:254
msgid "ACF"
msgstr ""

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr ""

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr ""

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr ""

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr ""

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr ""

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr ""

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr ""

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr ""

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr ""

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr ""

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr ""

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr ""

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[η προεπισκόπηση σύντομου κώδικα απενεργοποιήθηκε]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "Κλείσιμο αναδυομένου"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field moved to other group"
msgstr "Το πεδίο μετακινήθηκε σε άλλη ομάδα"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Close modal"
msgstr "Κλείσιμο αναδυομένου"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "Ξεκινήστε μια νέα ομάδα από καρτέλες σε αυτή την καρτέλα."

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "Νέα Ομάδα Καρτελών"

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Παρουσιάστε τα checkbox στυλιζαρισμένα χρησιμοποιώντας το select2"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "Αποθήκευση Άλλης Επιλογής"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "Να Επιτρέπονται Άλλες Επιλογές"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr "Προσθήκη Εναλλαγής Όλων"

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "Αποθήκευση Προσαρμοσμένων Τιμών"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "Να Επιτρέπονται Προσαρμοσμένες Τιμές"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"Οι προσαρμοσμένες τιμές των checkbox δεν επιτρέπεται να είναι κενές. "
"Αποεπιλέξετε τις κενές τιμές."

#: includes/admin/views/global/navigation.php:256
msgid "Updates"
msgstr "Ανανεώσεις"

#: includes/admin/views/global/navigation.php:180
#: includes/admin/views/global/navigation.php:184
msgid "Advanced Custom Fields logo"
msgstr "Λογότυπος Advanced Custom Fields"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "Αποθήκευση Αλλαγών"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "Τίτλος Ομάδας Πεδίων"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Προσθήκη τίτλου"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"Είστε καινούριοι στο ACF; Κάνετε μια περιήγηση στον <a href=\"%s\" "
"target=\"_blank\">οδηγό εκκίνησης για νέους</a>."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "Προσθήκη Ομάδας Πεδίων"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"To ACF χρησιμοποιεί <a href=\"%s\" target=\"_blank\">ομάδες πεδίων</a> για "
"να ομαδοποιήσει προσαρμοσμένα πεδία και να τα παρουσιάσει στις οθόνες "
"επεξεργασίας."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "Προσθέστε την Πρώτη σας Ομάδα Πεδίων"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:258
msgid "Options Pages"
msgstr "Σελίδες Ρυθμίσεων"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "ACF Blocks"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "Πεδίο Gallery"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "Πεδίο Flexible Content"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "Πεδίο Repeater"

#: includes/admin/views/global/navigation.php:218
msgid "Unlock Extra Features with ACF PRO"
msgstr "Ξεκλειδώστε Επιπλέον Δυνατότητες με το ACF PRO"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "Διαγραφή Ομάδας Πεδίων"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "Δημιουργήθηκε την %1$s στις %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Ρυθμίσεις Ομάδας"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Κανόνες Τοποθεσίας"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Επιλέξτε από περισσότερους από 30 τύπους πεδίων. <a href=\"%s\" "
"target=\"_blank\">Μάθετε περισσότερα</a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Ξεκινήστε να δημιουργείτε νέα προσαρμοσμένα πεδία για τα άρθρα, τις σελίδες, "
"τα custom post types και γενικότερα το περιεχόμενο του WordPress."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "Προσθέστε το Πρώτο σας Πεδίο"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "Προσθήκη Πεδίου"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "Παρουσίαση"

#: includes/fields.php:383
msgid "Validation"
msgstr "Επικύρωση"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "Γενικά"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "Εισαγωγή JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "Εξαγωγή ως JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "Η ομάδα πεδίων έχει απενεργοποιηθεί."
msgstr[1] "%s ομάδες πεδίων έχουν απενεργοποιηθεί."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "Η ομάδα πεδίων ενεργοποιήθηκε."
msgstr[1] "%s ομάδες πεδίων ενεργοποιήθηκαν."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "Απενεργοποίηση"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "Απενεργοποιήστε αυτό το αντικείμενο"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "Ενεργοποίηση"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "Ενεργοποιήστε αυτό το αντικείμενο"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr "Να μεταφερθεί αυτή η ομάδα πεδίων στον κάδο;"

#: acf.php:520 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "Ανενεργό"

#. Author of the plugin
#: acf.php includes/admin/views/global/navigation.php:240
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:578
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""

#: acf.php:576
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "Το %1$s πρέπει να έχει έναν χρήστη με ρόλο %2$s."
msgstr[1] ""
"Το %1$s πρέπει να έχει έναν χρήστη με έναν από τους παρακάτω ρόλους: %2$s."

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "Το %1$s πρέπει να έχει ένα έγκυρο ID χρήστη."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Μη έγκυρο αίτημα."

#: includes/fields/class-acf-field-select.php:689
msgid "%1$s is not one of %2$s"
msgstr "Το %1$s δεν είναι ένα από τα %2$s."

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "To %1$s πρέπει να έχει τον όρο %2$s."
msgstr[1] "To %1$s πρέπει να έχει έναν από τους παρακάτω όρους: %2$s."

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "Το %1$s πρέπει να έχει post type %2$s."
msgstr[1] "Το %1$s πρέπει να έχει ένα από τα παρακάτω post type: %2$s."

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "Το %1$s πρέπει να έχει ένα έγκυρο post ID."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "Το %s απαιτεί ένα έγκυρο attachment ID."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "Να εμφανίζεται στο REST API"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Ενεργοποίηση Διαφάνειας"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "RGBA Array"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "RGBA String"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "Hex String"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr ""

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Ενεργό"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "Το '%s' δεν είναι έγκυρη διεύθυνση email."

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Τιμή χρώματος"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Επιλέξτε το προεπιλεγμένο χρώμα"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Εκκαθάριση χρώματος"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Μπλοκ"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Επιλογές"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Χρήστες"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Στοιχεία μενού"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Μικροεφαρμογές"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Συνημμένα"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Ταξινομίες"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "Άρθρα"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Τελευταία ενημέρωση: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr ""

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Μη έγκυρες παράμετροι field group."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "Αναμονή αποθήκευσης"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "Αποθηκεύτηκε"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "Εισαγωγή"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Ανασκόπηση αλλαγών"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Βρίσκεται στο: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Βρίσκεται στο πρόσθετο: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Βρίσκεται στο θέμα: %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "Διάφορα"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Συγχρονισμός αλλαγών"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "Φόρτωση διαφορών"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Ανασκόπηση τοπικών αλλαγών στο JSON"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "Επισκεφθείτε τον ιστότοπο"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "Προβολή λεπτομερειών"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "Έκδοση %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "Πληροφορία"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Υποστήριξη</a>. Οι επαγγελματίες στην "
"Υποστήριξή μας θα σας βοηθήσουν με τις πιο προχωρημένες τεχνικές δυσκολίες "
"σας."

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Τεκμηρίωση</a>. Η εκτεταμένη μας τεκμηρίωσή "
"περιέχει αναφορές και οδηγούς για τις περισσότερες από τις περιπτώσεις που "
"τυχόν συναντήσετε. "

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Είμαστε παθιασμένοι σχετικά με την υποστήριξη και θέλουμε να καταφέρετε το "
"καλύτερο μέσα από τον ιστότοπό σας με το ACF. Αν συναντήσετε δυσκολίες, "
"υπάρχουν πολλά σημεία στα οποία μπορείτε να αναζητήσετε βοήθεια:"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "Βοήθεια & Υποστήριξη"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Χρησιμοποιήστε την καρτέλα Βοήθεια & Υποστήριξη για να επικοινωνήστε μαζί "
"μας στην περίπτωση που χρειαστείτε βοήθεια. "

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Προτού δημιουργήσετε το πρώτο σας Field Group, σας συστήνουμε να διαβάσετε "
"τον οδηγό μας <a href=\"%s\" target=\"_blank\">Getting started</a> για να "
"εξοικειωθείτε με τη φιλοσοφία του προσθέτου και τις βέλτιστες πρακτικές του. "

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Το πρόσθετο Advanced Custom Fields παρέχει έναν οπτικό κατασκευαστή φορμών "
"που σας επιτρέπει να προσαρμόζετε τις οθόνες επεξεργασίας του WordPress με "
"νέα πεδία, καθώς και ένα διαισθητικό API για να παρουσιάζετε τις τιμές των "
"custom field σε οποιοδήποτε template file ενός θέματος. "

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "Επισκόπηση"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "Ο τύπος τοποθεσίας \"%s\" είναι ήδη καταχωρημένος."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "Η κλάση \"%s\" δεν υπάρχει."

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Μη έγκυρο nonce."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Σφάλμα κατά τη φόρτωση του πεδίου."

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Σφάλμα</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Μικροεφαρμογή"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Ρόλος Χρήστη"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Σχόλιο"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Μορφή Άρθρου"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Στοιχείο Μενού"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Κατάσταση Άρθρου"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Μενού"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Τοποθεσίες Μενού"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Μενού"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Ταξινομία Άρθρου"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Υποσελίδα (έχει γονέα)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Γονική Σελίδα (έχει απογόνους)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Σελίδα Ανωτάτου Επιπέδου (χωρίς γονέα)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Σελίδα Άρθρων"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Αρχική Σελίδα"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Τύπος Άρθρου"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Προβολή του back end"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Προβολή του front end"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Συνδεδεμένος"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Τρέχων Χρήστης"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Πρότυπο Σελίδας"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Εγγραφή"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Προσθήκη / Επεξεργασία"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Φόρμα Χρήστη"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Γονέας Σελίδας"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Υπερδιαχειριστής"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Ρόλος Τρέχοντος Χρήστη"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Προεπιλεγμένο Πρότυπο"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Πρότυπο Άρθρου"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Κατηγορία Άρθρου"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Όλες οι %s μορφές"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Συνημμένο"

#: includes/validation.php:323
msgid "%s value is required"
msgstr "Η τιμή του %s είναι υποχρεωτική"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Εμφάνιση αυτού του πεδίου αν"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "Λογική Υπό Συνθήκες"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "και"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "Τοπικό JSON"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Πεδίο Κλώνου"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Επιβεβαιώστε επίσης ότι όλα τα επί πληρωμή πρόσθετα (%s) είναι ενημερωμένα "
"στην τελευταία τους έκδοση."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Αυτή η έκδοση περιέχει βελτιώσεις στη βάση δεδομένων σας κι απαιτεί μια "
"αναβάθμιση."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Ευχαριστούμε που αναβαθμίσατε στο  %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Απαιτείται Αναβάθμιση Βάσης Δεδομένων"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Σελίδα Επιλογών"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "Συλλογή"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "Ευέλικτο Περιεχόμενο"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "Επαναλήπτης"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Πίσω σε όλα τα εργαλεία"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Αν περισσότερα από ένα field group εμφανίζονται στην οθόνη επεξεργασίας, "
"τότε θα χρησιμοποιηθούν οι ρυθμίσεις του πρώτου (αυτού με το χαμηλότερον "
"αριθμό σειράς)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Επιλέξτε</b> στοιχεία για να τα <b>αποκρύψετε</b> από την οθόνη "
"τροποποίησης."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Απόκρυψη σε οθόνη"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Αποστολή Παραπομπών"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "Ετικέτες"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "Κατηγορίες"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Χαρακτηριστικά Σελίδας"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Μορφή"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Συντάκτης"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Σύντομο όνομα"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Αναθεωρήσεις"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Σχόλια"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Συζήτηση"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Απόσπασμα"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Επεξεργαστής Περιεχομένου"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Μόνιμος σύνδεσμος"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Εμφανίζεται στη λίστα ομάδας πεδίων"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "Ομάδες πεδίων με χαμηλότερη σειρά θα εμφανιστούν πρώτες"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "Αρ. Παραγγελίας"

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Παρακάτω πεδία"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Παρακάτω ετικέτες"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Πλάι"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Κανονικό (μετά το περιεχόμενο)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Ψηλά (μετά τον τίτλο)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Τοποθεσία"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Ενοποιημένη (χωρίς metabox)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Κλασική (με WP metabox)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Στυλ"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Τύπος"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Κλειδί"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Σειρά"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Κλείσιμο Πεδίου"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "κλάση"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "πλάτος"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Ιδιότητες Πλαισίου"

#: includes/fields/class-acf-field.php:312
msgid "Required"
msgstr "Απαιτείται"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Οδηγίες"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Τύπος Πεδίου"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Μια λέξη, χωρίς κενά. Επιτρέπονται κάτω παύλες και παύλες"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Όνομα Πεδίου"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Αυτό είναι το όνομα που θα εμφανιστεί στην σελίδα ΤΡΟΠΟΠΟΙΗΣΗΣ."

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Επιγραφή Πεδίου"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Διαγραφή"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Διαγραφή πεδίου"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Μετακίνηση"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Μετακίνηση του πεδίου σε άλλο group"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Δημιουργία αντιγράφου του πεδίου"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Τροποποίηση πεδίου"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Σύρετε για αναδιάταξη"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "Εμφάνιση αυτής της ομάδας πεδίου αν"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Δεν υπάρχουν διαθέσιμες ενημερώσεις."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Η αναβάθμιση της βάσης δεδομένων ολοκληρώθηκε. <a href=\"%s\">Δείτε τι νέο "
"υπάρχει</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "Πραγματοποιείται ανάγνωση εργασιών αναβάθμισης..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Η αναβάθμιση απέτυχε."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Η αναβάθμιση ολοκληρώθηκε."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "Πραγματοποιείται αναβάθμιση δεδομένων στην έκδοση %s"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Συνιστάται ιδιαίτερα να πάρετε αντίγραφο ασφαλείας της βάσης δεδομένων σας "
"προτού να συνεχίσετε. Είστε βέβαιοι ότι θέλετε να εκτελέσετε την ενημέρωση "
"τώρα;"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Παρακαλούμε επιλέξτε τουλάχιστον έναν ιστότοπο για αναβάθμιση."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Η Αναβάθμιση της Βάσης Δεδομένων ολοκληρώθηκε. <a href=\"%s\">Επιστροφή στον "
"πίνακα ελέγχου του δικτύου</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "Ο ιστότοπος είναι ενημερωμένος"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Ο ιστότοπος απαιτεί αναβάθμιση της βάσης δεδομένων από %1$s σε%2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Ιστότοπος"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Αναβάθμιση Ιστοτόπων"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Οι παρακάτω ιστότοποι απαιτούν αναβάθμιση της Βάσης Δεδομένων. Επιλέξτε "
"αυτούς που θέλετε να ενημερώσετε και πατήστε το %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Προσθήκη ομάδας κανόνων"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Δημιουργήστε μια ομάδα κανόνων που θα καθορίζουν ποιες επεξεργασίας θα "
"χρησιμοποιούν αυτά τα πεδία"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Κανόνες"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Αντιγράφηκε"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Αντιγραφή στο πρόχειρο"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Επιλογή ομάδων πεδίων"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Δεν επιλέχθηκε καμία ομάδα πεδίων"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "Δημιουργία PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Εξαγωγή Ομάδων Πεδίων"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "Εισαγωγή κενού αρχείου"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Εσφαλμένος τύπος αρχείου"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "Σφάλμα μεταφόρτωσης αρχείου. Παρακαλούμε δοκιμάστε πάλι"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Εισαγωγή Ομάδων Πεδίων"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Συγχρονισμός"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "Επιλογή %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Δημιουργία αντιγράφου"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Δημιουργία αντιγράφου αυτού του στοιχείου"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr ""

#: includes/admin/admin.php:355
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Τεκμηρίωση"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Περιγραφή"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Διαθέσιμος συγχρονισμός"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] ""
msgstr[1] ""

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Δημιουργήθηκε αντίγραφο της ομάδας πεδίων."
msgstr[1] "Δημιουργήθηκαν αντίγραφα %s ομάδων πεδίων."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Ενεργό <span class=\"count\">(%s)</span>"
msgstr[1] "Ενεργά <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Ανασκόπηση ιστοτόπων & αναβάθμιση"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Αναβάθμιση Βάσης Δεδομένων"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Custom Fields"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "Μετακίνηση Πεδίου"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "Παρακαλούμε επιλέξτε τον προορισμό γι' αυτό το πεδίο"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "Το πεδίο %1$s μπορεί πλέον να βρεθεί στην ομάδα πεδίων %2$s"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "Η Μετακίνηση Ολοκληρώθηκε."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Ενεργό"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "Field Keys"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "Ρυθμίσεις"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Τοποθεσία"

#: includes/admin/post-types/admin-field-group.php:100
msgid "Null"
msgstr "Null"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "αντιγραφή"

#: includes/admin/post-types/admin-field-group.php:96
msgid "(this field)"
msgstr "(αυτό το πεδίο)"

#: includes/admin/post-types/admin-field-group.php:94
msgid "Checked"
msgstr "Επιλεγμένο"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "Μετακίνηση Προσαρμοσμένου Πεδίου"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "Δεν υπάρχουν διαθέσιμα πεδία εναλλαγής"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "Ο τίτλος του field group είναι απαραίτητος"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr ""
"Αυτό το πεδίο δεν μπορεί να μετακινηθεί μέχρι να αποθηκευτούν οι αλλαγές του"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr ""
"Το αλφαριθμητικό \"field_\" δεν μπορεί να χρησιμοποιηθεί στην αρχή ενός "
"ονόματος πεδίου"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Το πρόχειρο field group ενημερώθηκε"

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Το field group προγραμματίστηκε."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Το field group καταχωρήθηκε."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Το field group αποθηκεύτηκε. "

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Το field group δημοσιεύθηκε."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Το field group διαγράφηκε."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Το field group ενημερώθηκε."

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:254
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "Εργαλεία"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "δεν είναι ίσο με"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "είναι ίσο με"

#: includes/locations.php:104
msgid "Forms"
msgstr "Φόρμες"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Σελίδα"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Άρθρο"

#: includes/fields.php:328
msgid "Relational"
msgstr "Υπό Συνθήκες"

#: includes/fields.php:327
msgid "Choice"
msgstr "Επιλογή"

#: includes/fields.php:325
msgid "Basic"
msgstr "Βασικό"

#: includes/fields.php:276
msgid "Unknown"
msgstr "Άγνωστο"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "Ο τύπος πεδίου δεν υπάρχει"

#: includes/forms/form-front.php:220
msgid "Spam Detected"
msgstr "Άνιχνεύθηκε Spam"

#: includes/forms/form-front.php:103
msgid "Post updated"
msgstr "Το άρθρο ενημερώθηκε"

#: includes/forms/form-front.php:102
msgid "Update"
msgstr "Ενημέρωση"

#: includes/forms/form-front.php:63
msgid "Validate Email"
msgstr "Επιβεβαίωση Ηλεκτρονικού Ταχυδρομείου"

#: includes/fields.php:326 includes/forms/form-front.php:55
msgid "Content"
msgstr "Περιεχόμενο"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:46
msgid "Title"
msgstr "Τίτλος"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "Επεξεργασία field group"

#: includes/admin/post-types/admin-field-group.php:113
msgid "Selection is less than"
msgstr "Η επιλογή να είναι μικρότερη από"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is greater than"
msgstr "Η επιλογή να είναι μεγαλύτερη από"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Value is less than"
msgstr "Η τιμή να είναι μικρότερη από"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is greater than"
msgstr "Η τιμή να είναι μεγαλύτερη από"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value contains"
msgstr "Η τιμη να περιέχει"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value matches pattern"
msgstr "Η τιμή να ικανοποιεί το μοτίβο"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value is not equal to"
msgstr "Η τιμή να μην είναι ίση με"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is equal to"
msgstr "Η τιμή να είναι ίση με"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Has no value"
msgstr "Να μην έχει καμία τιμή"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has any value"
msgstr "Να έχει οποιαδήποτε τιμή"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
msgid "Cancel"
msgstr "Ακύρωση"

#: includes/assets.php:350
msgid "Are you sure?"
msgstr "Είστε σίγουροι;"

#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "%d πεδία χρήζουν προσοχής"

#: includes/assets.php:369
msgid "1 field requires attention"
msgstr "1 πεδίο χρήζει προσοχής"

#: includes/assets.php:368 includes/validation.php:257
#: includes/validation.php:265
msgid "Validation failed"
msgstr "Ο έλεγχος απέτυχε"

#: includes/assets.php:367
msgid "Validation successful"
msgstr "Ο έλεγχος πέτυχε"

#: includes/media.php:54
msgid "Restricted"
msgstr "Περιορισμένος"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Σύμπτυξη Λεπτομερειών"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Ανάπτυξη Λεπτομερειών"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "Να έχουν μεταφορτωθεί σε αυτή την ανάρτηση"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Ενημέρωση"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Επεξεργασία"

#: includes/assets.php:364
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Οι αλλαγές που έχετε κάνει θα χαθούν αν φύγετε από αυτή τη σελίδα."

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "Ο τύπος του πεδίου πρέπει να είναι %s."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "ή"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "Το μέγεθος του αρχείου πρέπει να το πολύ %s."

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "Το μέγεθος το αρχείου πρέπει να είναι τουλάχιστον %s."

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "Το ύψος της εικόνας πρέπει να είναι το πολύ %dpx."

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "Το ύψος της εικόνας πρέπει να είναι τουλάχιστον %dpx."

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "Το πλάτος της εικόνας πρέπει να είναι το πολύ %dpx."

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "Το πλάτος της εικόνας πρέπει να είναι τουλάχιστον %dpx."

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(χωρίς τίτλο)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "Πλήρες μέγεθος"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "Μεγάλο"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "Μεσαίο"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "Μικρογραφία"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
msgid "(no label)"
msgstr "(χωρίς ετικέτα)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Θέτει το ύψος του πεδίου κειμένου"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Γραμμές"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Πεδίο κειμένου πολλών γραμμών"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Εμφάνιση επιπλέον πεδίου checkbox που εναλλάσσει όλες τις επιλογές"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "Αποθήκευση των 'custom' τιμών στις επιλογές του πεδίου"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "Να επιτρέπεται η προσθήκη 'custom' τιμών"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "Προσθήκη νέας τιμής"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "Εναλλαγή Όλων"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "Να Επιτρέπονται τα URL των Archive"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "Archive"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Σύνδεσμος Σελίδας"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Προσθήκη"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "Όνομα"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s προστέθηκε"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s υπάρχει ήδη"

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "Ο Χρήστης δεν ήταν δυνατό να προσθέσει νέο %s"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "ID όρου"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "Object όρου"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "Φόρτωση τιμής από τους όρους της ανάρτησης"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "Φόρτωση Όρων"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "Σύνδεση επιλεγμένων όρων στο άρθρο"

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "Αποθήκευση Όρων"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "Να επιτρέπεται η δημιουργία νέων όρων κατά την επεξεργασία"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "Δημιουργία Όρων"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "Radio Button"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "Μοναδική Τιμή"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "Πολλαπλή Επιλογή"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "Checkbox"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "Πολλαπλές Τιμές"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "Επιλέξτε την εμφάνιση αυτού του πεδίου"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "Εμφάνιση"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "Επιλέξτε την ταξινομία οπυ θέλετε να εμφανιστεί"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "Κανένα %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "Η τιμή πρέπι να είναι ίση ή μικρότερη από %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "Η τιμή πρέπει να είναι ίση ή μεγαλύτερη από %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "Η τιμή πρέπει να είναι αριθμός"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Αριθμός"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "Αποθήκευση των \"άλλων\" τιμών στις επιλογές του πεδίου"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "Προσθήκη επιλογής \"άλλο\" ώστε να επιτρέπονται προσαρμοσμένες τιμές"

#: includes/admin/views/global/navigation.php:202
msgid "Other"
msgstr "Άλλο"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Radio Button"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Προσδιορίστε ένα σημείο άκρου στο οποίο το προηγούμενο accordion κλείνει. "
"Αυτό δε θα είναι κάτι ορατό."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr "Επιτρέψτε σε αυτό το accordion να ανοίγει χωρίς να κλείνουν τα άλλα."

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "Αυτό το accordion να είναι ανοιχτό κατά τη φόρτωση της σελίδας."

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "Άνοιγμα"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Ακορντεόν"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Περιορίστε τα είδη των αρχείων που επιτρέπονται για μεταφόρτωση"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "ID Αρχείου"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "URL Αρχείου"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Array Αρχείων"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Προσθήκη Αρχείου"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "Δεν επιλέχθηκε αρχείο"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "Όνομα αρχείου"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "Ενημέρωση Αρχείου"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "Επεξεργασία Αρχείου"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "Επιλογή Αρχείου"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "Αρχείο"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Συνθηματικό"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "Προσδιορίστε την επιστρεφόμενη τιμή"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "Χρήση AJAX για τη φόρτωση των τιμών με καθυστέρηση;"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "Εισαγάγετε την κάθε προεπιλεγμένη τιμή σε μια νέα γραμμή"

#: includes/fields/class-acf-field-select.php:217 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "Επιλέξτε"

#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Η φόρτωση απέτυχε"

#: includes/fields/class-acf-field-select.php:94
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Αναζήτηση&hellip;"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Φόρτωση περισσότερων αποτελεσμάτων&hellip;"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Μπορείτε να επιλέξετε μόνο %d αντικείμενα"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Μπορείτε να επιλέξετε μόνο 1 αντικείμενο"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Παρακαλούμε διαγράψτε %d χαρακτήρες"

#: includes/fields/class-acf-field-select.php:87
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Παρακαλούμε διαγράψτε 1 χαρακτήρα"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Παρακαλούμε εισαγάγετε %d ή περισσότερους χαρακτήρες"

#: includes/fields/class-acf-field-select.php:84
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Παρακαλούμε εισαγάγετε 1 ή περισσότερους χαρακτήρες"

#: includes/fields/class-acf-field-select.php:83
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Δε βρέθηκαν αποτελέσματα που να ταιριάζουν"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:82
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"Υπάρχουν %d αποτελέσματα διαθέσιμα, χρησιμοποιήσετε τα πλήκτρα πάνω και κάτω "
"για να μεταβείτε σε αυτά."

#: includes/fields/class-acf-field-select.php:80
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Ένα αποτέλεσμα είναι διαθέσιμο, πατήστε enter για να το επιλέξετε."

#: includes/fields/class-acf-field-select.php:16
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "Επιλογή"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "ID Χρήστη"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "Object Χρήστη"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "Array Χρήστη"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "Όλοι οι ρόλοι χρηστών"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr ""

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Χρήστης"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Διαχωριστικό"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Επιλογή Χρώματος"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "Προεπιλογή"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Καθαρισμός"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Επιλογέας Χρωμάτων"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "ΜΜ"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "ΜΜ"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "ΠΜ"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "ΠΜ"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Επιλογή"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Ολοκληρώθηκε"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Τώρα"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Ζώνη Ώρας"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Μικροδευτερόλεπτο"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Χιλιοστό του δευτερολέπτου"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Δευτερόλεπτο"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Λεπτό"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Ώρα"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Ώρα"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Επιλέξτε Ώρα"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Επιλογέας Ημερομηνίας και Ώρας"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "Endpoint"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Αριστερή στοίχιση"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Στοίχιση στην κορυφή"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Τοποθέτηση"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Καρτέλα"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "Η τιμή πρέπει να είναι ένα έγκυρο URL "

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "Σύνδεσμος URL"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Link Array"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "Ανοίγει σε νέο παράθυρο/καρτέλα"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Επιλογή Συνδέσμου"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Σύνδεσμος"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Μέγεθος Βήματος"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "Μέγιστη Τιμή"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "Ελάχιστη Τιμή"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Εύρος"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "Και τα δύο (Array)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "Ετικέτα"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "Τιμή"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Κατακόρυφα"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Οριζόντια"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "κόκκινο : Κόκκινο"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Για μεγαλύτερο έλεγχο μπορείτε να δηλώσετε μια τιμή και μια ετικέτα ως εξής:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "Προσθέστε κάθε επιλογή σε μια νέα γραμμή."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "Επιλογές"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Ομάδα Κουμπιών"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "Γονέας"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "Ο TinyMCE δε θα αρχικοποιηθεί έως ότου ο χρήστης κλικάρει το πεδίο"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Γραμμή εργαλείων"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Μόνο Κείμενο"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Μόνο Οπτικός"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Οπτικός & Κείμενο"

#: includes/fields/class-acf-field-icon_picker.php:262
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Καρτέλες"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "Κάνετε κλικ για να αρχικοποιηθεί ο TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Κείμενο"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Οπτικός"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "Η τιμή πρέπει να μην ξεπερνά τους %d χαρακτήρες"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Αφήστε κενό για να μην υπάρχει όριο"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Όριο Χαρακτήρων"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Εμφανίζεται μετά το πεδίο εισαγωγής"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Προσάρτηση στο τέλος"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Εμφανίζεται πριν το πεδίο εισαγωγής"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Προσάρτηση στην αρχή"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Εμφανίζεται εντός του πεδίου εισαγωγής"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Υποκατάστατο Κείμενο"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Εμφανίζεται κατά τη δημιουργία νέου post"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Κείμενο"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "Το %1$s απαιτεί τουλάχιστον %2$s επιλογή"
msgstr[1] "Το %1$s απαιτεί τουλάχιστον %2$s επιλογές"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "Post ID"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "Post Object"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr ""

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "Επιλεγμένη Εικόνα"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "Τα επιλεγμένα αντικείμενα θα εμφανίζονται σε κάθε αποτέλεσμα"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "Αντικείμενα"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Ταξινομία"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Τύπος Άρθρου"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "Φίλτρα"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "Όλες οι Ταξινομίες"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "Φιλτράρισμα κατά Ταξινομία"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "Όλοι οι τύποι άρθρων"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "Φιλτράρισμα κατά Τύπο Άρθρου"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "Αναζήτηση..."

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "Επιλογή ταξινομίας"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "Επιλογή τύπου άρθρου"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "Δεν βρέθηκαν αντιστοιχίες"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "Φόρτωση"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "Αγγίξατε το μέγιστο πλήθος τιμών ( {max} τιμές )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Σχέση"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "Λίστα διαχωρισμένη με κόμμα. Αφήστε κενό για όλους τους τύπους"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr ""

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "Μέγιστο"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "Μέγεθος αρχείου"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Περιορίστε ποιες εικόνες μπορούν να μεταφορτωθούν"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "Ελάχιστο"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "Μεταφορτώθηκε στο άρθρο"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Όλα"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Περιορισμός της επιλογής βιβλιοθήκης πολυμέσων"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Βιβλιοθήκη"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Μέγεθος Προεπισκόπησης"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "Image ID"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "URL Εικόνας"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Image Array"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "Προσδιορίστε την επιστρεφόμενη τιμή παρουσίασης"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "Επιστρεφόμενη Τιμή"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Προσθήκη Εικόνας"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "Δεν επιλέχθηκε εικόνα"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "Αφαίρεση"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Επεξεργασία"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "Όλες οι εικόνες"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "Ενημέρωση Εικόνας"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "Επεξεργασία Εικόνας"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "Επιλογή Εικόνας"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Εικόνα"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Να επιτρέπεται η HTML markup να παρουσιαστεί ως ορατό κείμενο αντί να "
"αποδοθεί"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "Escape HTML"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "Χωρίς Μορφοποίηση"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Αυτόματη προσθήκη &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Αυτόματη προσθήκη παραγράφων"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Ελέγχει πώς αποδίδονται οι αλλαγές γραμμής"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "Αλλαγές Γραμμής"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "Η Εβδομάδα Αρχίζει Την"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "Η μορφή που χρησιμοποιείται στην αποθήκευση μιας τιμής"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "Μορφή Αποθήκευσης"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "ΣΚ"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Προηγούμενο"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Επόμενο"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Σήμερα"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Ολοκλήρωση"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Επιλογέας Ημερομηνίας"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "Πλάτος"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "Διαστάσεις Embed"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "Εισάγετε URL"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "Κείμενο που εμφανίζεται όταν είναι ανενεργό"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "Ανενεργό Κείμενο"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Κείμενο που εμφανίζεται όταν είναι ενεργό"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Ενεργό Κείμενο"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "Στυλιζαρισμένο"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Προεπιλεγμένη Τιμή"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "Εμφανίζει κείμενο δίπλα στο πεδίο επιλογής"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Μήνυμα"

#: includes/assets.php:352 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: src/Site_Health/Site_Health.php:281 src/Site_Health/Site_Health.php:343
msgid "No"
msgstr "Όχι"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: src/Site_Health/Site_Health.php:280 src/Site_Health/Site_Health.php:343
msgid "Yes"
msgstr "Ναι"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "True / False"

#: includes/fields/class-acf-field-group.php:415
msgid "Row"
msgstr "Γραμμή"

#: includes/fields/class-acf-field-group.php:414
msgid "Table"
msgstr "Πίνακας"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:413
msgid "Block"
msgstr "Μπλοκ"

#: includes/fields/class-acf-field-group.php:408
msgid "Specify the style used to render the selected fields"
msgstr ""
"Επιλογή του στυλ που θα χρησιμοποιηθεί για την εμφάνιση των επιλεγμένων "
"πεδίων"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:407
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Διάταξη"

#: includes/fields/class-acf-field-group.php:391
msgid "Sub Fields"
msgstr "Υποπεδία"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "Ομάδα"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "Τροποποίηση ύψους χάρτη"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "Ύψος"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "Ορισμός αρχικού επιπέδου μεγέθυνσης"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "Μεγέθυνση"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "Κεντράρισμα αρχικού χάρτη"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "Κεντράρισμα"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "Αναζήτηση διεύθυνσης..."

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "Εύρεση τρέχουσας τοποθεσίας"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "Καθαρισμός τοποθεσίας"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "Αναζήτηση"

#: includes/fields/class-acf-field-google-map.php:57
msgid "Sorry, this browser does not support geolocation"
msgstr ""
"Λυπούμαστε, αυτός ο περιηγητής δεν υποστηρίζει λειτουργία γεωεντοπισμού"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "Χάρτης Google"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "Ο τύπος που επιστρέφεται μέσω των template functions"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:285
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Επιστρεφόμενος Τύπος"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Προσαρμοσμένο:"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "Ο τύπος που εμφανίζεται κατά την επεξεργασία ενός post"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Μορφή Εμφάνισης"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Επιλογέας Ώρας"

#. translators: counts for inactive field groups
#: acf.php:526
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Ανενεργό <span class=\"count\">(%s)</span>"
msgstr[1] "Ανενεργά <span class=\"count\">(%s)</span>"

#: acf.php:487
msgid "No Fields found in Trash"
msgstr "Δεν βρέθηκαν Πεδία στα Διεγραμμένα"

#: acf.php:486
msgid "No Fields found"
msgstr "Δεν βρέθηκαν Πεδία"

#: acf.php:485
msgid "Search Fields"
msgstr "Αναζήτηση Πεδίων"

#: acf.php:484
msgid "View Field"
msgstr "Προβολή Πεδίων"

#: acf.php:483 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Νέο Πεδίο"

#: acf.php:482
msgid "Edit Field"
msgstr "Επεξεργασία Πεδίου"

#: acf.php:481
msgid "Add New Field"
msgstr "Προσθήκη Νέου Πεδίου"

#: acf.php:479
msgid "Field"
msgstr "Πεδίο"

#: acf.php:478 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Πεδία"

#: acf.php:453
msgid "No Field Groups found in Trash"
msgstr "Δεν βρέθηκαν Ομάδες Πεδίων στα Διεγραμμένα"

#: acf.php:452
msgid "No Field Groups found"
msgstr "Δεν βρέθηκαν Ομάδες Πεδίων"

#: acf.php:451
msgid "Search Field Groups"
msgstr "Αναζήτηση Ομάδων Πεδίων "

#: acf.php:450
msgid "View Field Group"
msgstr "Προβολή Ομάδας Πεδίων"

#: acf.php:449
msgid "New Field Group"
msgstr "Νέα Ομάδα Πεδίων"

#: acf.php:448
msgid "Edit Field Group"
msgstr "Επεξεργασίας Ομάδας Πεδίων"

#: acf.php:447
msgid "Add New Field Group"
msgstr "Προσθήκη Νέας Ομάδας Πεδίων"

#: acf.php:446 acf.php:480
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Προσθήκη Νέου"

#: acf.php:445
msgid "Field Group"
msgstr "Ομάδα Πεδίου"

#: acf.php:444 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Ομάδες Πεδίων"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Προσαρμόστε το WordPress με ισχυρά, επαγγελματικά και εύχρηστα πεδία."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:290
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

<?php
/*
Plugin Name: CF7 Advanced Form Entries
Description: Enhances Contact Form 7 with duplicate prevention, QR login, and custom workflows.
Version: 1.0
Author: Your Name
*/

// Exit if accessed directly
if (! defined('ABSPATH')) exit;

// Register Custom Post Type for Form Entries
function cfae_register_form_entries_cpt()
{
    $labels = array(
        'name' => 'Form Entries',
        'singular_name' => 'Form Entry',
        'add_new' => 'Add New',
        'add_new_item' => 'Add New Form Entry',
        'edit_item' => 'Edit Form Entry',
        'new_item' => 'New Form Entry',
        'view_item' => 'View Form Entry',
        'search_items' => 'Search Form Entries',
        'not_found' => 'No Form Entries found',
        'not_found_in_trash' => 'No Form Entries found in Trash',
        'all_items' => 'All Form Entries',
        'menu_name' => 'Form Entries',
    );
    $args = array(
        'labels' => $labels,
        'public' => false,
        'show_ui' => true,
        'supports' => array('title', 'custom-fields'),
        'capability_type' => 'post',
        'menu_position' => 25,
        'menu_icon' => 'dashicons-forms',
    );
    register_post_type('form_entries', $args);
}
add_action('init', 'cfae_register_form_entries_cpt');

// Add custom columns to Form Entries admin list
function cfae_add_form_entries_columns($columns)
{
    $new_columns = array();
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = 'Customer Name';
    $new_columns['customer_email'] = 'Email Address';
    $new_columns['customer_phone'] = 'Phone Number';
    $new_columns['submission_date'] = 'Submission Date';
    $new_columns['login_key'] = 'Login Key';
    return $new_columns;
}
add_filter('manage_form_entries_posts_columns', 'cfae_add_form_entries_columns');

// Populate custom columns with data
function cfae_populate_form_entries_columns($column, $post_id)
{
    switch ($column) {
        case 'customer_email':
            echo esc_html(get_post_meta($post_id, 'email-address', true));
            break;
        case 'customer_phone':
            echo esc_html(get_post_meta($post_id, 'phone-number', true));
            break;
        case 'submission_date':
            echo get_the_date('Y-m-d H:i:s', $post_id);
            break;
        case 'login_key':
            global $wpdb;
            $table = $wpdb->prefix . 'cfae_entry_keys';
            $row = $wpdb->get_row($wpdb->prepare("SELECT login_key FROM $table WHERE user_id = %d", $post_id));
            echo $row ? esc_html($row->login_key) : 'N/A';
            break;
    }
}
add_action('manage_form_entries_posts_custom_column', 'cfae_populate_form_entries_columns', 10, 2);

// Make custom columns sortable
function cfae_make_form_entries_columns_sortable($columns)
{
    $columns['customer_email'] = 'customer_email';
    $columns['customer_phone'] = 'customer_phone';
    $columns['submission_date'] = 'date';
    return $columns;
}
add_filter('manage_edit-form_entries_sortable_columns', 'cfae_make_form_entries_columns_sortable');

// Handle sorting for custom columns
function cfae_form_entries_orderby($query)
{
    if (!is_admin() || !$query->is_main_query()) {
        return;
    }

    if ('form_entries' !== $query->get('post_type')) {
        return;
    }

    $orderby = $query->get('orderby');

    if ('customer_email' === $orderby) {
        $query->set('meta_key', 'email-address');
        $query->set('orderby', 'meta_value');
    } elseif ('customer_phone' === $orderby) {
        $query->set('meta_key', 'phone-number');
        $query->set('orderby', 'meta_value');
    }
}
add_action('pre_get_posts', 'cfae_form_entries_orderby');

// Plugin activation: Create custom table for login keys
function cfae_activate_plugin()
{
    global $wpdb;
    $table_name = $wpdb->prefix . 'cfae_entry_keys';
    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
        user_id BIGINT UNSIGNED NOT NULL,
        login_key VARCHAR(64) NOT NULL,
        email VARCHAR(255) NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_id (user_id)
    ) $charset_collate;";
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}
register_activation_hook(__FILE__, 'cfae_activate_plugin');

// Duplicate entry prevention for email and phone in CF7
function cfae_cf7_duplicate_entry_validation($result, $tag)
{
    $tag_name = $tag['name'];
    if ($tag_name === 'email-address' || $tag_name === 'phone-number') {
        $value = isset($_POST[$tag_name]) ? sanitize_text_field($_POST[$tag_name]) : '';
        $args = array(
            'post_type' => 'form_entries',
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => $tag_name,
                    'value' => $value,
                    'compare' => '='
                )
            )
        );
        $query = new WP_Query($args);
        if ($query->have_posts()) {
            $result->invalidate($tag, __('This ' . ($tag_name === 'email-address' ? 'email' : 'phone number') . ' already exists.', 'cf7-advanced-form-entries'));
        }
    }
    return $result;
}
add_filter('wpcf7_validate_email*', 'cfae_cf7_duplicate_entry_validation', 10, 2);
add_filter('wpcf7_validate_tel*', 'cfae_cf7_duplicate_entry_validation', 10, 2);

// Handle CF7 submission: save to CPT, generate login key, QR code, and send emails
function cfae_cf7_handle_submission($contact_form)
{
    $submission = WPCF7_Submission::get_instance();
    if (!$submission) return;
    $data = $submission->get_posted_data();

    // Only process for the specific form (update ID as needed)
    $target_form_id = '4be63d7'; // Update this to your actual form ID if needed
    if ($contact_form->id() != $target_form_id) return;

    $user_email = isset($data['email-address']) ? sanitize_email($data['email-address']) : '';
    $user_phone = isset($data['phone-number']) ? sanitize_text_field($data['phone-number']) : '';
    $user_name = isset($data['full-name']) ? sanitize_text_field($data['full-name']) : '';

    // Save to CPT
    $post_id = wp_insert_post(array(
        'post_type' => 'form_entries',
        'post_title' => $user_name,
        'post_status' => 'publish',
        'meta_input' => array(
            'email-address' => $user_email,
            'phone-number' => $user_phone,
            'form_data' => maybe_serialize($data)
        )
    ));

    // Generate unique login key
    $login_key = wp_generate_password(8, false);
    global $wpdb;
    $table_name = $wpdb->prefix . 'cfae_entry_keys';
    $wpdb->replace($table_name, array(
        'user_id' => $post_id,
        'login_key' => $login_key,
        'email' => $user_email
    ));

    // Generate QR code (Google Chart API)
    $qr_url = home_url('/login?user_id=' . $post_id);
    $qr_code_img = 'https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=' . urlencode($qr_url);

    // Prepare user email HTML
    $user_email_html = file_get_contents(__DIR__ . '/user-email-template.html');
    $user_email_html = str_replace('[LOGIN_KEY_PLACEHOLDER]', esc_html($login_key), $user_email_html);
    $user_email_html = str_replace('[QR_URL_PLACEHOLDER]', esc_url($qr_url), $user_email_html);
    $user_email_html = str_replace('QR Code Will Appear Here', '<img src="' . esc_url($qr_code_img) . '" alt="QR Code" width="200" height="200" />', $user_email_html);

    // Send user email
    wp_mail($user_email, 'Your Submission Confirmation', $user_email_html, array('Content-Type: text/html; charset=UTF-8'));

    // Send admin email (simple notification)
    $admin_email = get_option('admin_email');
    $admin_subject = 'New Form Submission Received';
    $admin_message = 'A new form submission has been received from ' . $user_name . ' (' . $user_email . ').';
    wp_mail($admin_email, $admin_subject, $admin_message);
}
add_action('wpcf7_mail_sent', 'cfae_cf7_handle_submission');

// Start PHP session if not already started
function cfae_start_session()
{
    if (!session_id()) {
        session_start();
    }
}
add_action('init', 'cfae_start_session', 1);

// Shortcode: [cfae_user_login]
function cfae_user_login_shortcode()
{
    global $wpdb;
    $output = '';
    $user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;
    if (!$user_id) {
        return '<div class="cfae-error">Invalid access. No user ID provided.</div>';
    }

    // Handle logout
    if (isset($_GET['cfae_logout'])) {
        unset($_SESSION['cfae_logged_in_' . $user_id]);
        unset($_SESSION['cfae_login_time_' . $user_id]);
        wp_redirect(remove_query_arg('cfae_logout'));
        exit;
    }

    // Check session for 15-min login
    $session_key = 'cfae_logged_in_' . $user_id;
    $session_time = 'cfae_login_time_' . $user_id;
    $is_logged_in = isset($_SESSION[$session_key]) && $_SESSION[$session_key] === true;
    $login_time = isset($_SESSION[$session_time]) ? $_SESSION[$session_time] : 0;
    $now = time();
    if ($is_logged_in && ($now - $login_time > 900)) { // 15 min = 900 sec
        unset($_SESSION[$session_key]);
        unset($_SESSION[$session_time]);
        $is_logged_in = false;
    }

    // Handle login form submission
    if (!$is_logged_in && isset($_POST['cfae_login_key'])) {
        $input_key = sanitize_text_field($_POST['cfae_login_key']);
        $table = $wpdb->prefix . 'cfae_entry_keys';
        $row = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table WHERE user_id = %d", $user_id));
        if ($row && $input_key === $row->login_key) {
            $_SESSION[$session_key] = true;
            $_SESSION[$session_time] = $now;
            $is_logged_in = true;
        } else {
            $output .= '<div class="cfae-error">Invalid login key. Please try again.</div>';
        }
    }

    // Show login form if not logged in
    if (!$is_logged_in) {
        $output .= '<form method="post" class="cfae-login-form" autocomplete="off">';
        $output .= '<label for="cfae_login_key">Enter your Login Key:</label><br />';
        $output .= '<input type="password" id="cfae_login_key" name="cfae_login_key" required style="margin:10px 0; padding:8px; width:250px;" /><br />';
        $output .= '<button type="submit" style="padding:8px 20px;">Login</button>';
        $output .= '</form>';
        return cfae_prevent_screenshot_js() . $output;
    }

    // Fetch all submissions for this user (by email)
    $table = $wpdb->prefix . 'cfae_entry_keys';
    $row = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table WHERE user_id = %d", $user_id));
    $user_email = $row ? $row->email : '';
    $entries = get_posts(array(
        'post_type' => 'form_entries',
        'post_status' => 'publish',
        'meta_query' => array(
            array(
                'key' => 'email-address',
                'value' => $user_email,
                'compare' => '='
            )
        ),
        'numberposts' => -1
    ));
    $output .= '<div class="cfae-user-entries">';
    $output .= '<h2>Your Submissions</h2>';
    $output .= '<a href="' . esc_url(add_query_arg('cfae_logout', '1')) . '" style="float:right;">Logout</a>';
    if ($entries) {
        foreach ($entries as $entry) {
            $form_data = maybe_unserialize(get_post_meta($entry->ID, 'form_data', true));
            $output .= '<div class="cfae-entry" style="margin-bottom:30px;">';
            $output .= '<h3>Submission: ' . esc_html($entry->post_title) . '</h3>';
            $output .= '<table border="1" cellpadding="6" style="border-collapse:collapse; background:#fff;">';
            foreach ($form_data as $field => $value) {
                $output .= '<tr><th style="background:#f4f4f4;">' . esc_html($field) . '</th><td>' . esc_html(is_array($value) ? implode(", ", $value) : $value) . '</td></tr>';
            }
            $output .= '</table>';
            $output .= '</div>';
        }
    } else {
        $output .= '<p>No submissions found.</p>';
    }
    $output .= '</div>';
    $output .= cfae_prevent_screenshot_js();
    return $output;
}
add_shortcode('cfae_user_login', 'cfae_user_login_shortcode');

// JS/CSS to prevent screenshot/download/print (best effort)
function cfae_prevent_screenshot_js()
{
    ob_start();
?>
    <script>
        document.addEventListener('contextmenu', event => event.preventDefault());
        document.addEventListener('keydown', function(e) {
            if (e.key === 'PrintScreen' || (e.ctrlKey && e.key === 'p')) {
                e.preventDefault();
            }
            if (e.keyCode == 44) { // PrtScn
                e.preventDefault();
            }
        });
        document.body.style.webkitUserSelect = 'none';
        document.body.style.userSelect = 'none';
        document.body.style.webkitTouchCallout = 'none';
    </script>
    <style>
        body,
        .cfae-user-entries {
            -webkit-user-select: none;
            user-select: none;
        }
    </style>
<?php
    return ob_get_clean();
}

// On admin update of form_entries, notify user with updated QR and login key
function cfae_notify_user_on_admin_update($post_id, $post, $update)
{
    // Only for form_entries CPT and only on update (not new post)
    if ($post->post_type !== 'form_entries' || !$update) return;

    $user_email = get_post_meta($post_id, 'email-address', true);
    if (!$user_email) return;

    // Get login key from custom table
    global $wpdb;
    $table = $wpdb->prefix . 'cfae_entry_keys';
    $row = $wpdb->get_row($wpdb->prepare("SELECT * FROM $table WHERE user_id = %d", $post_id));
    if (!$row) return;
    $login_key = $row->login_key;

    // Generate QR code (Google Chart API)
    $qr_url = home_url('/login?user_id=' . $post_id);
    $qr_code_img = 'https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=' . urlencode($qr_url);

    // Prepare user email HTML
    $user_email_html = file_get_contents(__DIR__ . '/user-email-template.html');
    $user_email_html = str_replace('[LOGIN_KEY_PLACEHOLDER]', esc_html($login_key), $user_email_html);
    $user_email_html = str_replace('[QR_URL_PLACEHOLDER]', esc_url($qr_url), $user_email_html);
    $user_email_html = str_replace('QR Code Will Appear Here', '<img src="' . esc_url($qr_code_img) . '" alt="QR Code" width="200" height="200" />', $user_email_html);
    // Add admin change notice
    $admin_notice = '<div style="background:#ffeeba; color:#856404; padding:15px; border-radius:5px; margin-bottom:20px; border-left:4px solid #ffc107; font-weight:bold;">Note: Your submission data was updated by the admin. Please review your details below.</div>';
    $user_email_html = preg_replace('/(<div class=\"content\">)/', '$1' . $admin_notice, $user_email_html, 1);

    // Send user email
    wp_mail($user_email, 'Your Submission Was Updated by Admin', $user_email_html, array('Content-Type: text/html; charset=UTF-8'));
}
add_action('post_updated', 'cfae_notify_user_on_admin_update', 10, 3);

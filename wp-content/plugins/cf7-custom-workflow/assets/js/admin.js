/**
 * Admin JavaScript for CF7 Custom Workflow
 */

(function($) {
    'use strict';
    
    var CF7CW_Admin = {
        
        init: function() {
            this.initTabs();
            this.initEmailTemplates();
            this.initTestEmail();
            this.initFormValidation();
            this.initTooltips();
        },
        
        initTabs: function() {
            $('.cf7cw-tabs a').on('click', function(e) {
                e.preventDefault();
                
                var $this = $(this);
                var target = $this.attr('href');
                
                // Update active tab
                $('.cf7cw-tabs a').removeClass('active');
                $this.addClass('active');
                
                // Show target content
                $('.cf7cw-tab-content').removeClass('active');
                $(target).addClass('active');
            });
        },
        
        initEmailTemplates: function() {
            $('.cf7cw-template-form').on('submit', function(e) {
                e.preventDefault();
                
                var $form = $(this);
                var $button = $form.find('.button-primary');
                var originalText = $button.text();
                
                // Show loading state
                $button.prop('disabled', true)
                       .html('<span class="cf7cw-spinner"></span>Saving...');
                
                $.ajax({
                    url: cf7cw_admin.ajax_url,
                    type: 'POST',
                    data: $form.serialize() + '&action=cf7cw_update_email_template',
                    success: function(response) {
                        if (response.success) {
                            CF7CW_Admin.showMessage('success', cf7cw_admin.messages.settings_saved);
                        } else {
                            CF7CW_Admin.showMessage('error', response.data || cf7cw_admin.messages.error);
                        }
                    },
                    error: function() {
                        CF7CW_Admin.showMessage('error', cf7cw_admin.messages.error);
                    },
                    complete: function() {
                        $button.prop('disabled', false).text(originalText);
                    }
                });
            });
        },
        
        initTestEmail: function() {
            $('.cf7cw-test-email').on('click', function(e) {
                e.preventDefault();
                
                var $button = $(this);
                var templateType = $button.data('template');
                var testEmail = prompt('Enter email address to send test email:');
                
                if (!testEmail || !CF7CW_Admin.isValidEmail(testEmail)) {
                    alert('Please enter a valid email address.');
                    return;
                }
                
                var originalText = $button.text();
                $button.prop('disabled', true)
                       .html('<span class="cf7cw-spinner"></span>Sending...');
                
                $.ajax({
                    url: cf7cw_admin.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'cf7cw_test_email',
                        template_type: templateType,
                        test_email: testEmail,
                        nonce: cf7cw_admin.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            CF7CW_Admin.showMessage('success', cf7cw_admin.messages.email_sent);
                        } else {
                            CF7CW_Admin.showMessage('error', response.data || cf7cw_admin.messages.error);
                        }
                    },
                    error: function() {
                        CF7CW_Admin.showMessage('error', cf7cw_admin.messages.error);
                    },
                    complete: function() {
                        $button.prop('disabled', false).text(originalText);
                    }
                });
            });
        },
        
        initFormValidation: function() {
            // Real-time validation for email fields
            $('input[type="email"]').on('blur', function() {
                var $input = $(this);
                var email = $input.val();
                
                if (email && !CF7CW_Admin.isValidEmail(email)) {
                    $input.addClass('error');
                    CF7CW_Admin.showFieldError($input, 'Please enter a valid email address.');
                } else {
                    $input.removeClass('error');
                    CF7CW_Admin.hideFieldError($input);
                }
            });
            
            // Validation for numeric fields
            $('input[type="number"]').on('blur', function() {
                var $input = $(this);
                var value = parseInt($input.val());
                var min = parseInt($input.attr('min'));
                var max = parseInt($input.attr('max'));
                
                if (value < min || value > max) {
                    $input.addClass('error');
                    CF7CW_Admin.showFieldError($input, 'Value must be between ' + min + ' and ' + max + '.');
                } else {
                    $input.removeClass('error');
                    CF7CW_Admin.hideFieldError($input);
                }
            });
        },
        
        initTooltips: function() {
            // Simple tooltip implementation
            $('[data-tooltip]').on('mouseenter', function() {
                var $this = $(this);
                var tooltipText = $this.data('tooltip');
                
                var $tooltip = $('<div class="cf7cw-tooltip">' + tooltipText + '</div>');
                $tooltip.css({
                    position: 'absolute',
                    background: '#333',
                    color: '#fff',
                    padding: '8px 12px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    zIndex: '9999',
                    whiteSpace: 'nowrap',
                    top: $this.offset().top - 35,
                    left: $this.offset().left
                });
                
                $('body').append($tooltip);
                $this.data('tooltip-element', $tooltip);
            }).on('mouseleave', function() {
                var $this = $(this);
                var $tooltip = $this.data('tooltip-element');
                if ($tooltip) {
                    $tooltip.remove();
                    $this.removeData('tooltip-element');
                }
            });
        },
        
        showMessage: function(type, message) {
            var $message = $('<div class="cf7cw-message ' + type + '">' + message + '</div>');
            
            // Remove existing messages
            $('.cf7cw-message').remove();
            
            // Add new message
            $('.wrap h1').after($message);
            
            // Auto-hide after 5 seconds
            setTimeout(function() {
                $message.fadeOut(function() {
                    $message.remove();
                });
            }, 5000);
            
            // Scroll to top to show message
            $('html, body').animate({ scrollTop: 0 }, 300);
        },
        
        showFieldError: function($field, message) {
            var $error = $field.siblings('.cf7cw-field-error');
            
            if ($error.length === 0) {
                $error = $('<div class="cf7cw-field-error">' + message + '</div>');
                $error.css({
                    color: '#dc3545',
                    fontSize: '12px',
                    marginTop: '5px'
                });
                $field.after($error);
            } else {
                $error.text(message);
            }
        },
        
        hideFieldError: function($field) {
            $field.siblings('.cf7cw-field-error').remove();
        },
        
        isValidEmail: function(email) {
            var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },
        
        // Code editor initialization (if CodeMirror is available)
        initCodeEditor: function(selector, mode) {
            if (typeof wp !== 'undefined' && wp.codeEditor) {
                var settings = wp.codeEditor.defaultSettings ? _.clone(wp.codeEditor.defaultSettings) : {};
                settings.codemirror = _.extend({}, settings.codemirror, {
                    mode: mode || 'htmlmixed',
                    lineNumbers: true,
                    lineWrapping: true,
                    theme: 'default'
                });
                
                $(selector).each(function() {
                    wp.codeEditor.initialize(this, settings);
                });
            }
        },
        
        // Statistics refresh
        refreshStats: function() {
            $.ajax({
                url: cf7cw_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'cf7cw_refresh_stats',
                    nonce: cf7cw_admin.nonce
                },
                success: function(response) {
                    if (response.success && response.data) {
                        $('.cf7cw-stat-card').each(function() {
                            var $card = $(this);
                            var statType = $card.data('stat');
                            if (response.data[statType]) {
                                $card.find('h3').text(response.data[statType]);
                            }
                        });
                    }
                }
            });
        },
        
        // Export functionality
        initExport: function() {
            $('.cf7cw-export-btn').on('click', function(e) {
                e.preventDefault();
                
                var $button = $(this);
                var exportType = $button.data('export');
                var originalText = $button.text();
                
                $button.prop('disabled', true)
                       .html('<span class="cf7cw-spinner"></span>Exporting...');
                
                // Create a temporary form for file download
                var $form = $('<form>', {
                    method: 'POST',
                    action: cf7cw_admin.ajax_url
                });
                
                $form.append($('<input>', {
                    type: 'hidden',
                    name: 'action',
                    value: 'cf7cw_export_' + exportType
                }));
                
                $form.append($('<input>', {
                    type: 'hidden',
                    name: 'nonce',
                    value: cf7cw_admin.nonce
                }));
                
                $('body').append($form);
                $form.submit();
                $form.remove();
                
                setTimeout(function() {
                    $button.prop('disabled', false).text(originalText);
                }, 2000);
            });
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        CF7CW_Admin.init();
        CF7CW_Admin.initCodeEditor('.cf7cw-code-editor', 'htmlmixed');
        CF7CW_Admin.initExport();
        
        // Refresh stats every 30 seconds on settings page
        if ($('.cf7cw-stats-grid').length > 0) {
            setInterval(CF7CW_Admin.refreshStats, 30000);
        }
    });
    
})(jQuery);

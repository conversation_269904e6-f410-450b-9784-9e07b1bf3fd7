/**
 * Security JavaScript - Prevents unauthorized access and interactions
 */

(function($) {
    'use strict';
    
    var CF7CW_Security = {
        
        init: function() {
            this.disableRightClick();
            this.disableKeyboardShortcuts();
            this.disableTextSelection();
            this.disablePrinting();
            this.detectDevTools();
            this.preventImageSaving();
            this.initSessionTimer();
            this.addSecurityWarnings();
        },
        
        disableRightClick: function() {
            $(document).on('contextmenu', function(e) {
                e.preventDefault();
                CF7CW_Security.showSecurityMessage(cf7cw_security.messages.copy_blocked);
                return false;
            });
        },
        
        disableKeyboardShortcuts: function() {
            $(document).on('keydown', function(e) {
                // Disable F12 (Developer Tools)
                if (e.keyCode === 123) {
                    e.preventDefault();
                    CF7CW_Security.showSecurityMessage(cf7cw_security.messages.devtools_blocked);
                    return false;
                }
                
                // Disable Ctrl+Shift+I (Developer Tools)
                if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
                    e.preventDefault();
                    CF7CW_Security.showSecurityMessage(cf7cw_security.messages.devtools_blocked);
                    return false;
                }
                
                // Disable Ctrl+Shift+J (Console)
                if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
                    e.preventDefault();
                    CF7CW_Security.showSecurityMessage(cf7cw_security.messages.devtools_blocked);
                    return false;
                }
                
                // Disable Ctrl+U (View Source)
                if (e.ctrlKey && e.keyCode === 85) {
                    e.preventDefault();
                    CF7CW_Security.showSecurityMessage(cf7cw_security.messages.copy_blocked);
                    return false;
                }
                
                // Disable Ctrl+S (Save Page)
                if (e.ctrlKey && e.keyCode === 83) {
                    e.preventDefault();
                    CF7CW_Security.showSecurityMessage(cf7cw_security.messages.copy_blocked);
                    return false;
                }
                
                // Disable Ctrl+A (Select All)
                if (e.ctrlKey && e.keyCode === 65) {
                    e.preventDefault();
                    CF7CW_Security.showSecurityMessage(cf7cw_security.messages.copy_blocked);
                    return false;
                }
                
                // Disable Ctrl+P (Print)
                if (e.ctrlKey && e.keyCode === 80) {
                    e.preventDefault();
                    CF7CW_Security.showSecurityMessage(cf7cw_security.messages.print_blocked);
                    return false;
                }
                
                // Disable Ctrl+C (Copy)
                if (e.ctrlKey && e.keyCode === 67) {
                    e.preventDefault();
                    CF7CW_Security.showSecurityMessage(cf7cw_security.messages.copy_blocked);
                    return false;
                }
            });
        },
        
        disableTextSelection: function() {
            $(document).on('selectstart', function(e) {
                // Allow selection in form inputs
                if ($(e.target).is('input, textarea, [contenteditable]')) {
                    return true;
                }
                e.preventDefault();
                return false;
            });
            
            $(document).on('mousedown', function(e) {
                // Allow interaction with form inputs
                if ($(e.target).is('input, textarea, button, [contenteditable]')) {
                    return true;
                }
                e.preventDefault();
                return false;
            });
        },
        
        disablePrinting: function() {
            window.addEventListener('beforeprint', function(e) {
                e.preventDefault();
                CF7CW_Security.showSecurityMessage(cf7cw_security.messages.print_blocked);
                return false;
            });
            
            // Override print function
            window.print = function() {
                CF7CW_Security.showSecurityMessage(cf7cw_security.messages.print_blocked);
            };
        },
        
        detectDevTools: function() {
            var devtools = {
                open: false,
                orientation: null
            };
            
            var threshold = 160;
            
            setInterval(function() {
                if (window.outerHeight - window.innerHeight > threshold || 
                    window.outerWidth - window.innerWidth > threshold) {
                    if (!devtools.open) {
                        devtools.open = true;
                        CF7CW_Security.handleDevToolsDetection();
                    }
                } else {
                    devtools.open = false;
                }
            }, 500);
            
            // Console detection
            var element = new Image();
            Object.defineProperty(element, 'id', {
                get: function() {
                    CF7CW_Security.handleDevToolsDetection();
                }
            });
            
            setInterval(function() {
                console.log(element);
                console.clear();
            }, 1000);
        },
        
        handleDevToolsDetection: function() {
            alert(cf7cw_security.messages.devtools_blocked);
            window.location.href = '/';
        },
        
        preventImageSaving: function() {
            $(document).on('dragstart', 'img', function(e) {
                e.preventDefault();
                CF7CW_Security.showSecurityMessage(cf7cw_security.messages.copy_blocked);
                return false;
            });
            
            // Disable image context menu
            $('img').attr('oncontextmenu', 'return false;');
            $('img').attr('ondragstart', 'return false;');
            $('img').attr('onselectstart', 'return false;');
        },
        
        initSessionTimer: function() {
            if (typeof cf7cw_security.session_timeout === 'undefined') {
                return;
            }
            
            var sessionTimeout = cf7cw_security.session_timeout;
            var checkInterval = cf7cw_security.check_interval * 1000; // Convert to milliseconds
            
            setInterval(function() {
                $.ajax({
                    url: cf7cw_security.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'cf7cw_check_session',
                        nonce: cf7cw_security.nonce
                    },
                    success: function(response) {
                        if (!response.success) {
                            CF7CW_Security.handleSessionExpiry();
                        }
                    },
                    error: function() {
                        // Network error - assume session is still valid
                    }
                });
            }, checkInterval);
        },
        
        handleSessionExpiry: function() {
            alert(cf7cw_security.messages.session_expired);
            window.location.href = '/';
        },
        
        addSecurityWarnings: function() {
            // Add watermark
            if ($('.cf7cw-watermark').length === 0) {
                $('body').append('<div class="cf7cw-watermark"></div>');
            }
            
            // Add security notice if not present
            if ($('.cf7cw-security-notice').length === 0) {
                var notice = $('<div class="cf7cw-security-notice">' +
                    '<h4>Security Notice</h4>' +
                    '<ul>' +
                    '<li>Screenshots and copying are disabled</li>' +
                    '<li>This session will expire automatically</li>' +
                    '<li>Do not share your access credentials</li>' +
                    '</ul>' +
                    '</div>');
                
                $('body').append(notice);
            }
        },
        
        showSecurityMessage: function(message) {
            // Create and show temporary alert
            var alert = $('<div class="cf7cw-security-alert">' + message + '</div>');
            alert.css({
                position: 'fixed',
                top: '20px',
                right: '20px',
                background: '#dc3545',
                color: 'white',
                padding: '15px 20px',
                borderRadius: '5px',
                zIndex: '10000',
                boxShadow: '0 4px 15px rgba(0,0,0,0.2)'
            });
            
            $('body').append(alert);
            
            setTimeout(function() {
                alert.fadeOut(function() {
                    alert.remove();
                });
            }, 3000);
        },
        
        // Disable common bypass methods
        disableBypassMethods: function() {
            // Disable iframe embedding
            if (window.top !== window.self) {
                window.top.location = window.self.location;
            }
            
            // Clear console
            if (typeof console !== 'undefined') {
                console.clear();
                console.log = function() {};
                console.warn = function() {};
                console.error = function() {};
            }
            
            // Disable eval
            window.eval = function() {
                throw new Error('eval is disabled for security reasons');
            };
            
            // Disable setTimeout/setInterval with string arguments
            var originalSetTimeout = window.setTimeout;
            var originalSetInterval = window.setInterval;
            
            window.setTimeout = function(callback, delay) {
                if (typeof callback === 'string') {
                    throw new Error('setTimeout with string argument is disabled');
                }
                return originalSetTimeout.apply(this, arguments);
            };
            
            window.setInterval = function(callback, delay) {
                if (typeof callback === 'string') {
                    throw new Error('setInterval with string argument is disabled');
                }
                return originalSetInterval.apply(this, arguments);
            };
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        CF7CW_Security.init();
        CF7CW_Security.disableBypassMethods();
        
        // Add security class to body
        $('body').addClass('cf7cw-secure-page');
    });
    
    // Re-initialize on AJAX complete
    $(document).ajaxComplete(function() {
        CF7CW_Security.preventImageSaving();
    });
    
})(jQuery);

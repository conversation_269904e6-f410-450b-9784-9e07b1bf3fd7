/**
 * Security CSS - Prevents user interactions and selections
 */

/* Disable text selection globally on secure pages */
.cf7cw-secure-page * {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* Allow text selection in form inputs */
.cf7cw-secure-page input,
.cf7cw-secure-page textarea,
.cf7cw-secure-page [contenteditable] {
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    user-select: text !important;
}

/* Disable image dragging */
.cf7cw-secure-page img {
    pointer-events: none !important;
    -webkit-user-drag: none !important;
    -khtml-user-drag: none !important;
    -moz-user-drag: none !important;
    -o-user-drag: none !important;
    user-drag: none !important;
}

/* Hide content when printing */
@media print {
    .cf7cw-secure-page * {
        display: none !important;
    }
    
    .cf7cw-secure-page::before {
        content: "Printing is not allowed for this content.";
        display: block !important;
        text-align: center;
        font-size: 24px;
        color: #000;
        margin: 50px;
    }
}

/* Security watermark overlay */
.cf7cw-watermark {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 9999;
    background-image: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 100px,
        rgba(255, 0, 0, 0.05) 100px,
        rgba(255, 0, 0, 0.05) 120px
    );
    mix-blend-mode: multiply;
}

/* Security notice styling */
.cf7cw-security-notice {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 4px;
    font-size: 14px;
    color: #856404;
}

.cf7cw-security-notice h4 {
    margin: 0 0 10px 0;
    color: #856404;
    font-size: 16px;
}

.cf7cw-security-notice ul {
    margin: 0;
    padding-left: 20px;
}

.cf7cw-security-notice li {
    margin-bottom: 5px;
}

/* Disable context menu styling */
.cf7cw-no-context {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Session timeout warning */
.cf7cw-session-warning {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #dc3545;
    color: white;
    padding: 15px 20px;
    border-radius: 5px;
    z-index: 10000;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    animation: cf7cw-pulse 2s infinite;
}

@keyframes cf7cw-pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Secure form styling */
.cf7cw-secure-form {
    position: relative;
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.cf7cw-secure-form::before {
    content: "🔒";
    position: absolute;
    top: -10px;
    right: 15px;
    background: #fff;
    padding: 0 10px;
    font-size: 18px;
}

/* Disable developer tools detection overlay */
.cf7cw-devtools-warning {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99999;
    font-size: 24px;
    text-align: center;
}

/* Mobile responsive security features */
@media (max-width: 768px) {
    .cf7cw-session-warning {
        top: 10px;
        right: 10px;
        left: 10px;
        text-align: center;
        font-size: 14px;
        padding: 12px 15px;
    }
    
    .cf7cw-security-notice {
        margin: 15px 0;
        padding: 12px 15px;
    }
    
    .cf7cw-devtools-warning {
        font-size: 18px;
        padding: 20px;
    }
}

/* Accessibility considerations */
.cf7cw-secure-page [tabindex] {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

.cf7cw-secure-page [tabindex]:focus {
    outline: 2px solid #005a87;
}

/* Screen reader only content */
.cf7cw-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

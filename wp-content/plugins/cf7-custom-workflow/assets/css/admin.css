/**
 * Admin CSS for CF7 Custom Workflow
 */

/* Settings page styling */
.cf7cw-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.cf7cw-stat-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    transition: box-shadow 0.3s ease;
}

.cf7cw-stat-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,.1);
}

.cf7cw-stat-card h3 {
    font-size: 2.5em;
    margin: 0 0 10px 0;
    color: #0073aa;
    font-weight: 600;
}

.cf7cw-stat-card p {
    margin: 0;
    color: #666;
    font-weight: 500;
    font-size: 14px;
}

.cf7cw-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin: 20px 0;
}

.cf7cw-actions .button {
    margin-right: 0;
}

/* Email templates page */
.cf7cw-template-editor {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.cf7cw-template-editor h3 {
    margin-top: 0;
    color: #23282d;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
}

.cf7cw-template-field {
    margin-bottom: 20px;
}

.cf7cw-template-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #23282d;
}

.cf7cw-template-field input[type="text"],
.cf7cw-template-field textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.cf7cw-template-field textarea {
    min-height: 200px;
    font-family: 'Courier New', monospace;
    resize: vertical;
}

.cf7cw-template-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-top: 15px;
}

.cf7cw-template-actions .button-primary {
    background: #0073aa;
    border-color: #0073aa;
}

.cf7cw-template-actions .button-secondary {
    background: #f7f7f7;
    border-color: #ccd0d4;
    color: #555;
}

/* Security logs page */
.cf7cw-logs-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow: hidden;
}

.cf7cw-logs-table th,
.cf7cw-logs-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e1e1e1;
}

.cf7cw-logs-table th {
    background: #f7f7f7;
    font-weight: 600;
    color: #23282d;
}

.cf7cw-logs-table tr:hover {
    background: #f9f9f9;
}

.cf7cw-logs-table .event-type {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.cf7cw-logs-table .event-type.security {
    background: #dc3545;
    color: white;
}

.cf7cw-logs-table .event-type.login {
    background: #28a745;
    color: white;
}

.cf7cw-logs-table .event-type.access {
    background: #007cba;
    color: white;
}

.cf7cw-logs-table .event-type.warning {
    background: #ffc107;
    color: #212529;
}

/* Form entries meta box */
.cf7cw-meta-box {
    background: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    padding: 0;
}

.cf7cw-meta-table {
    width: 100%;
    border-collapse: collapse;
}

.cf7cw-meta-table th,
.cf7cw-meta-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e5e5e5;
}

.cf7cw-meta-table th {
    background: #f7f7f7;
    font-weight: 600;
    width: 30%;
}

.cf7cw-meta-table tr:last-child th,
.cf7cw-meta-table tr:last-child td {
    border-bottom: none;
}

.cf7cw-meta-table .empty-value {
    color: #999;
    font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
    .cf7cw-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .cf7cw-stat-card {
        padding: 15px;
    }
    
    .cf7cw-stat-card h3 {
        font-size: 2em;
    }
    
    .cf7cw-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .cf7cw-template-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .cf7cw-logs-table {
        font-size: 14px;
    }
    
    .cf7cw-logs-table th,
    .cf7cw-logs-table td {
        padding: 8px 10px;
    }
}

/* Loading states */
.cf7cw-loading {
    opacity: 0.6;
    pointer-events: none;
}

.cf7cw-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0073aa;
    border-radius: 50%;
    animation: cf7cw-spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes cf7cw-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error messages */
.cf7cw-message {
    padding: 12px 15px;
    border-radius: 4px;
    margin: 15px 0;
    font-weight: 500;
}

.cf7cw-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.cf7cw-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.cf7cw-message.warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.cf7cw-message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Help text */
.cf7cw-help-text {
    font-size: 13px;
    color: #666;
    margin-top: 5px;
    line-height: 1.4;
}

/* Code editor enhancements */
.cf7cw-code-editor {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.cf7cw-code-editor .CodeMirror {
    height: auto;
    min-height: 200px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
}

/* Tabs */
.cf7cw-tabs {
    border-bottom: 1px solid #ccd0d4;
    margin-bottom: 20px;
}

.cf7cw-tabs ul {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
}

.cf7cw-tabs li {
    margin: 0;
}

.cf7cw-tabs a {
    display: block;
    padding: 12px 20px;
    text-decoration: none;
    color: #555;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.cf7cw-tabs a:hover,
.cf7cw-tabs a.active {
    color: #0073aa;
    border-bottom-color: #0073aa;
}

.cf7cw-tab-content {
    display: none;
}

.cf7cw-tab-content.active {
    display: block;
}

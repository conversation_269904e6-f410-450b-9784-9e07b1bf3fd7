<?php
/**
 * Security Handler
 */

if (!defined('ABSPATH')) {
    exit;
}

class CF7CW_Security {
    
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_security_scripts'));
        add_action('wp_head', array($this, 'add_security_meta_tags'));
        add_action('wp_footer', array($this, 'add_security_scripts'));
        add_filter('wp_headers', array($this, 'add_security_headers'));
    }
    
    /**
     * Enqueue security scripts
     */
    public function enqueue_security_scripts() {
        // Only load on login page and user submissions page
        if ($this->is_secure_page()) {
            wp_enqueue_script(
                'cf7cw-security',
                CF7CW_PLUGIN_URL . 'assets/js/security.js',
                array('jquery'),
                CF7CW_VERSION,
                true
            );
            
            wp_localize_script('cf7cw-security', 'cf7cw_security', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('cf7cw_security_nonce'),
                'messages' => array(
                    'screenshot_blocked' => __('Screenshots are not allowed on this page.', CF7CW_TEXT_DOMAIN),
                    'copy_blocked' => __('Copying content is not allowed on this page.', CF7CW_TEXT_DOMAIN),
                    'print_blocked' => __('Printing is not allowed on this page.', CF7CW_TEXT_DOMAIN),
                    'devtools_blocked' => __('Developer tools are not allowed on this page.', CF7CW_TEXT_DOMAIN),
                    'session_expired' => __('Your session has expired. Please log in again.', CF7CW_TEXT_DOMAIN)
                ),
                'session_timeout' => 900, // 15 minutes
                'check_interval' => 60 // Check every minute
            ));
            
            wp_enqueue_style(
                'cf7cw-security',
                CF7CW_PLUGIN_URL . 'assets/css/security.css',
                array(),
                CF7CW_VERSION
            );
        }
    }
    
    /**
     * Add security meta tags
     */
    public function add_security_meta_tags() {
        if ($this->is_secure_page()) {
            echo '<meta name="robots" content="noindex, nofollow, noarchive, nosnippet, noimageindex">' . "\n";
            echo '<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">' . "\n";
            echo '<meta http-equiv="Pragma" content="no-cache">' . "\n";
            echo '<meta http-equiv="Expires" content="0">' . "\n";
        }
    }
    
    /**
     * Add security scripts in footer
     */
    public function add_security_scripts() {
        if ($this->is_secure_page()) {
            ?>
            <script type="text/javascript">
            // Disable right-click context menu
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                return false;
            });
            
            // Disable text selection
            document.addEventListener('selectstart', function(e) {
                e.preventDefault();
                return false;
            });
            
            // Disable drag and drop
            document.addEventListener('dragstart', function(e) {
                e.preventDefault();
                return false;
            });
            
            // Disable keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Disable F12, Ctrl+Shift+I, Ctrl+Shift+J, Ctrl+U, Ctrl+S, Ctrl+A, Ctrl+P, Ctrl+C
                if (e.keyCode === 123 || 
                    (e.ctrlKey && e.shiftKey && (e.keyCode === 73 || e.keyCode === 74)) ||
                    (e.ctrlKey && (e.keyCode === 85 || e.keyCode === 83 || e.keyCode === 65 || e.keyCode === 80 || e.keyCode === 67))) {
                    e.preventDefault();
                    return false;
                }
            });
            
            // Disable print
            window.addEventListener('beforeprint', function(e) {
                e.preventDefault();
                alert('<?php echo esc_js(__('Printing is not allowed on this page.', CF7CW_TEXT_DOMAIN)); ?>');
                return false;
            });
            
            // Detect developer tools
            let devtools = {
                open: false,
                orientation: null
            };
            
            const threshold = 160;
            
            setInterval(function() {
                if (window.outerHeight - window.innerHeight > threshold || 
                    window.outerWidth - window.innerWidth > threshold) {
                    if (!devtools.open) {
                        devtools.open = true;
                        alert('<?php echo esc_js(__('Developer tools are not allowed on this page.', CF7CW_TEXT_DOMAIN)); ?>');
                        window.location.href = '<?php echo esc_url(home_url()); ?>';
                    }
                } else {
                    devtools.open = false;
                }
            }, 500);
            
            // Disable image saving
            document.addEventListener('dragstart', function(e) {
                if (e.target.tagName === 'IMG') {
                    e.preventDefault();
                }
            });
            
            // Add CSS to prevent selection and disable user interactions
            const style = document.createElement('style');
            style.textContent = `
                * {
                    -webkit-user-select: none !important;
                    -moz-user-select: none !important;
                    -ms-user-select: none !important;
                    user-select: none !important;
                    -webkit-touch-callout: none !important;
                    -webkit-tap-highlight-color: transparent !important;
                }
                
                input, textarea {
                    -webkit-user-select: text !important;
                    -moz-user-select: text !important;
                    -ms-user-select: text !important;
                    user-select: text !important;
                }
                
                img {
                    pointer-events: none !important;
                    -webkit-user-drag: none !important;
                    -khtml-user-drag: none !important;
                    -moz-user-drag: none !important;
                    -o-user-drag: none !important;
                    user-drag: none !important;
                }
                
                @media print {
                    * {
                        display: none !important;
                    }
                }
            `;
            document.head.appendChild(style);
            </script>
            <?php
        }
    }
    
    /**
     * Add security headers
     */
    public function add_security_headers($headers) {
        if ($this->is_secure_page()) {
            $headers['X-Frame-Options'] = 'DENY';
            $headers['X-Content-Type-Options'] = 'nosniff';
            $headers['X-XSS-Protection'] = '1; mode=block';
            $headers['Referrer-Policy'] = 'no-referrer';
            $headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';";
        }
        
        return $headers;
    }
    
    /**
     * Check if current page is a secure page
     */
    private function is_secure_page() {
        global $post;
        
        // Check if it's the login page
        if ($post && $post->post_name === 'form-login') {
            return true;
        }
        
        // Check if user is viewing submissions
        if (isset($_SESSION['cf7cw_user_id'])) {
            return true;
        }
        
        // Check for specific query parameters
        if (isset($_GET['user_id']) && is_numeric($_GET['user_id'])) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Validate session and check for timeout
     */
    public function validate_session_security() {
        if (!$this->is_secure_page()) {
            return true;
        }
        
        $login_system = new CF7CW_Login_System();
        
        if (!$login_system->validate_session()) {
            // Session invalid or expired
            wp_redirect(home_url());
            exit;
        }
        
        return true;
    }
    
    /**
     * Add watermark to prevent screenshots
     */
    public function add_watermark() {
        if ($this->is_secure_page()) {
            ?>
            <div id="cf7cw-watermark" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 9999;
                background-image: repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 100px,
                    rgba(255,0,0,0.1) 100px,
                    rgba(255,0,0,0.1) 120px
                );
                mix-blend-mode: multiply;
            "></div>
            <?php
        }
    }
    
    /**
     * Log security events
     */
    public function log_security_event($event_type, $details = array()) {
        $log_entry = array(
            'timestamp' => current_time('mysql'),
            'event_type' => $event_type,
            'ip_address' => $this->get_client_ip(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'details' => $details
        );
        
        error_log('CF7CW Security Event: ' . json_encode($log_entry));
        
        // Store in database for admin review
        $this->store_security_log($log_entry);
    }
    
    /**
     * Store security log in database
     */
    private function store_security_log($log_entry) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'cf7cw_security_logs';
        
        // Create table if it doesn't exist
        $this->create_security_logs_table();
        
        $wpdb->insert(
            $table_name,
            array(
                'event_type' => $log_entry['event_type'],
                'ip_address' => $log_entry['ip_address'],
                'user_agent' => $log_entry['user_agent'],
                'details' => json_encode($log_entry['details']),
                'created_at' => $log_entry['timestamp']
            ),
            array('%s', '%s', '%s', '%s', '%s')
        );
    }
    
    /**
     * Create security logs table
     */
    private function create_security_logs_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'cf7cw_security_logs';
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE IF NOT EXISTS $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            event_type varchar(50) NOT NULL,
            ip_address varchar(45) NOT NULL,
            user_agent text,
            details text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY event_type (event_type),
            KEY ip_address (ip_address),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }
    
    /**
     * Clean up old security logs
     */
    public function cleanup_security_logs() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'cf7cw_security_logs';
        
        // Delete logs older than 30 days
        $wpdb->query("DELETE FROM $table_name WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)");
    }
}

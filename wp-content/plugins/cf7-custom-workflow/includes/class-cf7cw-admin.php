<?php
/**
 * Admin Interface Handler
 */

if (!defined('ABSPATH')) {
    exit;
}

class CF7CW_Admin {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_cf7cw_save_settings', array($this, 'save_settings'));
        add_action('wp_ajax_cf7cw_test_email', array($this, 'test_email'));
        add_action('admin_notices', array($this, 'admin_notices'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('CF7 Custom Workflow', CF7CW_TEXT_DOMAIN),
            __('CF7 Workflow', CF7CW_TEXT_DOMAIN),
            'manage_options',
            'cf7cw-settings',
            array($this, 'settings_page'),
            'dashicons-forms',
            30
        );
        
        add_submenu_page(
            'cf7cw-settings',
            __('Settings', CF7CW_TEXT_DOMAIN),
            __('Settings', CF7CW_TEXT_DOMAIN),
            'manage_options',
            'cf7cw-settings',
            array($this, 'settings_page')
        );
        
        add_submenu_page(
            'cf7cw-settings',
            __('Email Templates', CF7CW_TEXT_DOMAIN),
            __('Email Templates', CF7CW_TEXT_DOMAIN),
            'manage_options',
            'cf7cw-email-templates',
            array($this, 'email_templates_page')
        );
        
        add_submenu_page(
            'cf7cw-settings',
            __('Security Logs', CF7CW_TEXT_DOMAIN),
            __('Security Logs', CF7CW_TEXT_DOMAIN),
            'manage_options',
            'cf7cw-security-logs',
            array($this, 'security_logs_page')
        );
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'cf7cw') !== false) {
            wp_enqueue_script(
                'cf7cw-admin',
                CF7CW_PLUGIN_URL . 'assets/js/admin.js',
                array('jquery'),
                CF7CW_VERSION,
                true
            );
            
            wp_localize_script('cf7cw-admin', 'cf7cw_admin', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('cf7cw_admin_nonce'),
                'messages' => array(
                    'settings_saved' => __('Settings saved successfully!', CF7CW_TEXT_DOMAIN),
                    'email_sent' => __('Test email sent successfully!', CF7CW_TEXT_DOMAIN),
                    'error' => __('An error occurred. Please try again.', CF7CW_TEXT_DOMAIN)
                )
            ));
            
            wp_enqueue_style(
                'cf7cw-admin',
                CF7CW_PLUGIN_URL . 'assets/css/admin.css',
                array(),
                CF7CW_VERSION
            );
            
            wp_enqueue_code_editor(array('type' => 'text/html'));
        }
    }
    
    /**
     * Settings page
     */
    public function settings_page() {
        $settings = get_option('cf7cw_settings', array());
        
        // Get all CF7 forms
        $cf7_forms = get_posts(array(
            'post_type' => 'wpcf7_contact_form',
            'numberposts' => -1,
            'post_status' => 'publish'
        ));
        
        include CF7CW_PLUGIN_PATH . 'templates/admin-settings.php';
    }
    
    /**
     * Email templates page
     */
    public function email_templates_page() {
        $email_handler = new CF7CW_Email_Handler();
        $templates = array(
            'user_confirmation' => $email_handler->get_email_template('user_confirmation'),
            'admin_notification' => $email_handler->get_email_template('admin_notification'),
            'admin_edit_notification' => $email_handler->get_email_template('admin_edit_notification')
        );
        
        include CF7CW_PLUGIN_PATH . 'templates/admin-email-templates.php';
    }
    
    /**
     * Security logs page
     */
    public function security_logs_page() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'cf7cw_security_logs';
        
        // Get logs with pagination
        $per_page = 20;
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $offset = ($page - 1) * $per_page;
        
        $logs = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_name ORDER BY created_at DESC LIMIT %d OFFSET %d",
            $per_page,
            $offset
        ));
        
        $total_logs = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
        $total_pages = ceil($total_logs / $per_page);
        
        include CF7CW_PLUGIN_PATH . 'templates/admin-security-logs.php';
    }
    
    /**
     * Save settings
     */
    public function save_settings() {
        check_ajax_referer('cf7cw_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $settings = array(
            'target_form_id' => sanitize_text_field($_POST['target_form_id']),
            'enable_duplicate_check' => isset($_POST['enable_duplicate_check']) ? 1 : 0,
            'enable_qr_codes' => isset($_POST['enable_qr_codes']) ? 1 : 0,
            'enable_security_features' => isset($_POST['enable_security_features']) ? 1 : 0,
            'session_timeout' => intval($_POST['session_timeout']),
            'key_expiry_days' => intval($_POST['key_expiry_days']),
            'admin_email' => sanitize_email($_POST['admin_email']),
            'from_name' => sanitize_text_field($_POST['from_name']),
            'from_email' => sanitize_email($_POST['from_email'])
        );
        
        update_option('cf7cw_settings', $settings);
        
        wp_send_json_success('Settings saved successfully');
    }
    
    /**
     * Test email functionality
     */
    public function test_email() {
        check_ajax_referer('cf7cw_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $test_email = sanitize_email($_POST['test_email']);
        $template_type = sanitize_text_field($_POST['template_type']);
        
        if (empty($test_email)) {
            wp_send_json_error('Please provide a valid email address');
        }
        
        $email_handler = new CF7CW_Email_Handler();
        
        switch ($template_type) {
            case 'user_confirmation':
                $result = $email_handler->send_user_confirmation(999, $test_email, 'Test User', 'TEST123KEY');
                break;
                
            case 'admin_notification':
                $test_data = array(
                    'full-name' => 'Test User',
                    'email-address' => $test_email,
                    'phone-number' => '+1234567890'
                );
                $result = $email_handler->send_admin_notification($test_data);
                break;
                
            case 'admin_edit_notification':
                $test_changes = array(
                    'full-name' => array('old' => 'Old Name', 'new' => 'New Name'),
                    'email-address' => array('old' => '<EMAIL>', 'new' => $test_email)
                );
                $result = $email_handler->send_admin_edit_notification(999, $test_changes);
                break;
                
            default:
                wp_send_json_error('Invalid template type');
        }
        
        if ($result) {
            wp_send_json_success('Test email sent successfully');
        } else {
            wp_send_json_error('Failed to send test email');
        }
    }
    
    /**
     * Admin notices
     */
    public function admin_notices() {
        // Check if Contact Form 7 is active
        if (!class_exists('WPCF7_ContactForm')) {
            echo '<div class="notice notice-error"><p>';
            echo __('CF7 Custom Workflow requires Contact Form 7 to be installed and activated.', CF7CW_TEXT_DOMAIN);
            echo '</p></div>';
        }
        
        // Check if settings are configured
        $settings = get_option('cf7cw_settings', array());
        if (empty($settings['target_form_id'])) {
            echo '<div class="notice notice-warning"><p>';
            echo sprintf(
                __('Please configure your CF7 Custom Workflow settings. <a href="%s">Go to Settings</a>', CF7CW_TEXT_DOMAIN),
                admin_url('admin.php?page=cf7cw-settings')
            );
            echo '</p></div>';
        }
    }
    
    /**
     * Get plugin statistics
     */
    public function get_plugin_stats() {
        global $wpdb;
        
        $stats = array();
        
        // Total form entries
        $stats['total_entries'] = wp_count_posts('form_entries')->publish;
        
        // Active login keys
        $table_name = $wpdb->prefix . 'cf7cw_entry_keys';
        $stats['active_keys'] = $wpdb->get_var(
            "SELECT COUNT(*) FROM $table_name WHERE is_active = 1 AND expires_at > NOW()"
        );
        
        // Active sessions
        $sessions_table = $wpdb->prefix . 'cf7cw_user_sessions';
        $stats['active_sessions'] = $wpdb->get_var(
            "SELECT COUNT(*) FROM $sessions_table WHERE expires_at > NOW()"
        );
        
        // Recent entries (last 7 days)
        $stats['recent_entries'] = get_posts(array(
            'post_type' => 'form_entries',
            'date_query' => array(
                array(
                    'after' => '1 week ago'
                )
            ),
            'fields' => 'ids'
        ));
        $stats['recent_entries'] = count($stats['recent_entries']);
        
        return $stats;
    }
    
    /**
     * Export form entries
     */
    public function export_entries() {
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        $entries = get_posts(array(
            'post_type' => 'form_entries',
            'numberposts' => -1,
            'post_status' => 'publish'
        ));
        
        $csv_data = array();
        $csv_data[] = array(
            'ID', 'Name', 'Email', 'Phone', 'Submission Date',
            'Birth Date', 'Gender', 'Address', 'Country',
            'ID Number', 'Passport Number', 'Occupation', 'Employer'
        );
        
        foreach ($entries as $entry) {
            $csv_data[] = array(
                $entry->ID,
                get_post_meta($entry->ID, 'full-name', true),
                get_post_meta($entry->ID, 'email-address', true),
                get_post_meta($entry->ID, 'phone-number', true),
                get_the_date('Y-m-d H:i:s', $entry->ID),
                get_post_meta($entry->ID, 'birth-date', true),
                get_post_meta($entry->ID, 'gender', true),
                get_post_meta($entry->ID, 'address', true),
                get_post_meta($entry->ID, 'country-residence', true),
                get_post_meta($entry->ID, 'id-number', true),
                get_post_meta($entry->ID, 'passport-number', true),
                get_post_meta($entry->ID, 'occupation', true),
                get_post_meta($entry->ID, 'employer', true)
            );
        }
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="form-entries-' . date('Y-m-d') . '.csv"');
        
        $output = fopen('php://output', 'w');
        foreach ($csv_data as $row) {
            fputcsv($output, $row);
        }
        fclose($output);
        exit;
    }
}

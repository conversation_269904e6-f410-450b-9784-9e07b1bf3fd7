<?php
/**
 * <PERSON><PERSON> Handler
 */

if (!defined('ABSPATH')) {
    exit;
}

class CF7CW_Email_Handler {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_ajax_cf7cw_update_email_template', array($this, 'update_email_template'));
        add_action('wp_ajax_cf7cw_preview_email', array($this, 'preview_email'));
    }
    
    public function init() {
        // Initialize default email templates if they don't exist
        $this->init_default_templates();
    }
    
    /**
     * Send user confirmation email with QR code
     */
    public function send_user_confirmation($user_id, $user_email, $user_name, $login_key) {
        // Generate QR code
        $qr_generator = new CF7CW_QR_Generator();
        $qr_data = $qr_generator->generate_login_qr($user_id, $login_key);
        
        // Get email template
        $template = $this->get_email_template('user_confirmation');
        
        // Replace placeholders
        $placeholders = array(
            '[USER_NAME]' => $user_name,
            '[LOGIN_KEY]' => $login_key,
            '[QR_URL]' => $this->get_login_url($user_id),
            '[SITE_NAME]' => get_bloginfo('name'),
            '[SITE_URL]' => home_url(),
        );
        
        $subject = str_replace(array_keys($placeholders), array_values($placeholders), $template['subject']);
        $message = str_replace(array_keys($placeholders), array_values($placeholders), $template['message']);
        
        // Prepare headers
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
        );
        
        // Prepare attachments
        $attachments = array();
        if ($qr_data && isset($qr_data['path'])) {
            $attachments[] = $qr_data['path'];
        }
        
        // Send email
        $sent = wp_mail($user_email, $subject, $message, $headers, $attachments);
        
        if (!$sent) {
            error_log('Failed to send user confirmation email to: ' . $user_email);
        }
        
        return $sent;
    }
    
    /**
     * Send admin notification email
     */
    public function send_admin_notification($user_data) {
        $admin_email = get_option('admin_email');
        $template = $this->get_email_template('admin_notification');
        
        $placeholders = array(
            '[USER_NAME]' => $user_data['full-name'],
            '[USER_EMAIL]' => $user_data['email-address'],
            '[USER_PHONE]' => $user_data['phone-number'],
            '[SUBMISSION_DATE]' => current_time('Y-m-d H:i:s'),
            '[SITE_NAME]' => get_bloginfo('name'),
            '[ADMIN_URL]' => admin_url('edit.php?post_type=form_entries'),
        );
        
        $subject = str_replace(array_keys($placeholders), array_values($placeholders), $template['subject']);
        $message = str_replace(array_keys($placeholders), array_values($placeholders), $template['message']);
        
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
        );
        
        return wp_mail($admin_email, $subject, $message, $headers);
    }
    
    /**
     * Send admin edit notification to user
     */
    public function send_admin_edit_notification($post_id, $changed_fields) {
        $user_email = get_post_meta($post_id, 'email-address', true);
        $user_name = get_post_meta($post_id, 'full-name', true);
        
        if (empty($user_email)) {
            return false;
        }
        
        // Get login key for this user
        global $wpdb;
        $table_name = $wpdb->prefix . 'cf7cw_entry_keys';
        $key_data = $wpdb->get_row($wpdb->prepare(
            "SELECT login_key FROM $table_name WHERE user_id = %d AND is_active = 1",
            $post_id
        ));
        
        $login_key = $key_data ? $key_data->login_key : '';
        
        // Generate QR code
        $qr_generator = new CF7CW_QR_Generator();
        $qr_data = $qr_generator->get_user_qr_code($post_id);
        
        if (!$qr_data) {
            $qr_data = $qr_generator->generate_login_qr($post_id, $login_key);
        }
        
        // Build changes summary
        $changes_html = $this->build_changes_summary($changed_fields);
        
        $template = $this->get_email_template('admin_edit_notification');
        
        $placeholders = array(
            '[USER_NAME]' => $user_name,
            '[LOGIN_KEY]' => $login_key,
            '[QR_URL]' => $this->get_login_url($post_id),
            '[CHANGES_SUMMARY]' => $changes_html,
            '[SITE_NAME]' => get_bloginfo('name'),
            '[SITE_URL]' => home_url(),
        );
        
        $subject = str_replace(array_keys($placeholders), array_values($placeholders), $template['subject']);
        $message = str_replace(array_keys($placeholders), array_values($placeholders), $template['message']);
        
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
        );
        
        $attachments = array();
        if ($qr_data && isset($qr_data['path'])) {
            $attachments[] = $qr_data['path'];
        }
        
        return wp_mail($user_email, $subject, $message, $headers, $attachments);
    }
    
    /**
     * Build changes summary HTML
     */
    private function build_changes_summary($changed_fields) {
        $field_labels = array(
            'full-name' => __('Full Name', CF7CW_TEXT_DOMAIN),
            'birth-date' => __('Date of Birth', CF7CW_TEXT_DOMAIN),
            'gender' => __('Gender', CF7CW_TEXT_DOMAIN),
            'phone-number' => __('Phone Number', CF7CW_TEXT_DOMAIN),
            'email-address' => __('Email Address', CF7CW_TEXT_DOMAIN),
            'address' => __('Address', CF7CW_TEXT_DOMAIN),
            'country-residence' => __('Country of Residence', CF7CW_TEXT_DOMAIN),
            'identification-number' => __('Identification Number', CF7CW_TEXT_DOMAIN),
            'issuing-country' => __('Issuing Country', CF7CW_TEXT_DOMAIN),
            'expiration-date' => __('Expiration Date', CF7CW_TEXT_DOMAIN),
            'id-number' => __('National ID Number', CF7CW_TEXT_DOMAIN),
            'passport-number' => __('Passport Number', CF7CW_TEXT_DOMAIN),
            'place-issue' => __('Place of Issue', CF7CW_TEXT_DOMAIN),
            'date-issue' => __('Date of Issue', CF7CW_TEXT_DOMAIN),
            'contact-name' => __('Emergency Contact Name', CF7CW_TEXT_DOMAIN),
            'contact-relationship' => __('Contact Relationship', CF7CW_TEXT_DOMAIN),
            'contact-number' => __('Contact Phone Number', CF7CW_TEXT_DOMAIN),
            'contact-email' => __('Contact Email Address', CF7CW_TEXT_DOMAIN),
            'occupation' => __('Occupation', CF7CW_TEXT_DOMAIN),
            'employer' => __('Employer', CF7CW_TEXT_DOMAIN),
            'professional-aff' => __('Professional Affiliations', CF7CW_TEXT_DOMAIN),
        );
        
        $html = '<div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">';
        $html .= '<h3 style="color: #333; margin-top: 0;">' . __('Updated Information:', CF7CW_TEXT_DOMAIN) . '</h3>';
        $html .= '<table style="width: 100%; border-collapse: collapse;">';
        
        foreach ($changed_fields as $field => $change) {
            $label = isset($field_labels[$field]) ? $field_labels[$field] : ucfirst(str_replace('-', ' ', $field));
            
            $html .= '<tr style="border-bottom: 1px solid #ddd;">';
            $html .= '<td style="padding: 10px; font-weight: bold; width: 30%;">' . esc_html($label) . '</td>';
            $html .= '<td style="padding: 10px; width: 35%;">';
            $html .= '<span style="color: #dc3545; text-decoration: line-through;">' . esc_html($change['old']) . '</span>';
            $html .= '</td>';
            $html .= '<td style="padding: 10px; width: 35%;">';
            $html .= '<span style="color: #28a745; font-weight: bold;">' . esc_html($change['new']) . '</span>';
            $html .= '</td>';
            $html .= '</tr>';
        }
        
        $html .= '</table>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Get login URL
     */
    private function get_login_url($user_id) {
        $login_page = get_page_by_path('form-login');
        
        if ($login_page) {
            $login_url = get_permalink($login_page->ID);
        } else {
            $login_url = home_url('/form-login/');
        }
        
        return add_query_arg('user_id', $user_id, $login_url);
    }
    
    /**
     * Get email template
     */
    public function get_email_template($template_name) {
        $templates = get_option('cf7cw_email_templates', array());
        
        if (isset($templates[$template_name])) {
            return $templates[$template_name];
        }
        
        return $this->get_default_template($template_name);
    }
    
    /**
     * Update email template
     */
    public function update_email_template() {
        check_ajax_referer('cf7cw_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $template_name = sanitize_text_field($_POST['template_name']);
        $subject = sanitize_text_field($_POST['subject']);
        $message = wp_kses_post($_POST['message']);
        
        $templates = get_option('cf7cw_email_templates', array());
        $templates[$template_name] = array(
            'subject' => $subject,
            'message' => $message
        );
        
        update_option('cf7cw_email_templates', $templates);
        
        wp_send_json_success('Template updated successfully');
    }
    
    /**
     * Initialize default templates
     */
    private function init_default_templates() {
        $templates = get_option('cf7cw_email_templates', array());
        
        if (empty($templates)) {
            $default_templates = array(
                'user_confirmation' => $this->get_default_template('user_confirmation'),
                'admin_notification' => $this->get_default_template('admin_notification'),
                'admin_edit_notification' => $this->get_default_template('admin_edit_notification')
            );
            
            update_option('cf7cw_email_templates', $default_templates);
        }
    }
    
    /**
     * Get default template
     */
    private function get_default_template($template_name) {
        switch ($template_name) {
            case 'user_confirmation':
                return array(
                    'subject' => __('Submission Confirmation - [SITE_NAME]', CF7CW_TEXT_DOMAIN),
                    'message' => $this->get_user_confirmation_template()
                );
                
            case 'admin_notification':
                return array(
                    'subject' => __('New Form Submission - [USER_NAME]', CF7CW_TEXT_DOMAIN),
                    'message' => $this->get_admin_notification_template()
                );
                
            case 'admin_edit_notification':
                return array(
                    'subject' => __('Your Information Has Been Updated - [SITE_NAME]', CF7CW_TEXT_DOMAIN),
                    'message' => $this->get_admin_edit_template()
                );
                
            default:
                return array(
                    'subject' => '',
                    'message' => ''
                );
        }
    }
    
    /**
     * Get user confirmation template
     */
    private function get_user_confirmation_template() {
        ob_start();
        include CF7CW_PLUGIN_PATH . 'templates/email-user-confirmation.php';
        return ob_get_clean();
    }
    
    /**
     * Get admin notification template
     */
    private function get_admin_notification_template() {
        return '<h2>New Form Submission Received</h2>
                <p>A new form submission has been received from <strong>[USER_NAME]</strong>.</p>
                <p><strong>Details:</strong></p>
                <ul>
                    <li>Name: [USER_NAME]</li>
                    <li>Email: [USER_EMAIL]</li>
                    <li>Phone: [USER_PHONE]</li>
                    <li>Submission Date: [SUBMISSION_DATE]</li>
                </ul>
                <p><a href="[ADMIN_URL]">View all submissions in admin</a></p>';
    }
    
    /**
     * Get admin edit notification template
     */
    private function get_admin_edit_template() {
        return '<h2>Your Information Has Been Updated</h2>
                <p>Hello [USER_NAME],</p>
                <p>Your submission information has been updated by our admin team.</p>
                [CHANGES_SUMMARY]
                <p>You can view your updated information using your login credentials:</p>
                <p><strong>Login Key:</strong> [LOGIN_KEY]</p>
                <p><a href="[QR_URL]">Access your submissions</a></p>
                <p>If you have any questions, please contact us.</p>';
    }
}

<?php
/**
 * Form Validation Handler
 */

if (!defined('ABSPATH')) {
    exit;
}

class CF7CW_Validation {
    
    public function __construct() {
        add_filter('wpcf7_validate_email*', array($this, 'validate_email_duplicate'), 20, 2);
        add_filter('wpcf7_validate_tel*', array($this, 'validate_phone_duplicate'), 20, 2);
        add_filter('wpcf7_validate_email', array($this, 'validate_email_duplicate'), 20, 2);
        add_filter('wpcf7_validate_tel', array($this, 'validate_phone_duplicate'), 20, 2);
    }
    
    /**
     * Validate email for duplicates
     */
    public function validate_email_duplicate($result, $tag) {
        $tag_name = $tag->name;
        
        // Only check for email-address field
        if ($tag_name !== 'email-address') {
            return $result;
        }
        
        $value = isset($_POST[$tag_name]) ? sanitize_email($_POST[$tag_name]) : '';
        
        if (empty($value)) {
            return $result;
        }
        
        // Check for duplicate email in form_entries
        if ($this->check_duplicate_email($value)) {
            $result->invalidate($tag, __('This email address has already been used for a previous submission. Please use a different email address.', CF7CW_TEXT_DOMAIN));
        }
        
        return $result;
    }
    
    /**
     * Validate phone for duplicates
     */
    public function validate_phone_duplicate($result, $tag) {
        $tag_name = $tag->name;
        
        // Only check for phone-number field
        if ($tag_name !== 'phone-number') {
            return $result;
        }
        
        $value = isset($_POST[$tag_name]) ? sanitize_text_field($_POST[$tag_name]) : '';
        
        if (empty($value)) {
            return $result;
        }
        
        // Check for duplicate phone in form_entries
        if ($this->check_duplicate_phone($value)) {
            $result->invalidate($tag, __('This phone number has already been used for a previous submission. Please use a different phone number.', CF7CW_TEXT_DOMAIN));
        }
        
        return $result;
    }
    
    /**
     * Check if email already exists in form_entries
     */
    private function check_duplicate_email($email) {
        $args = array(
            'post_type' => 'form_entries',
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => 'email-address',
                    'value' => $email,
                    'compare' => '='
                )
            ),
            'fields' => 'ids',
            'posts_per_page' => 1
        );
        
        $query = new WP_Query($args);
        return $query->have_posts();
    }
    
    /**
     * Check if phone already exists in form_entries
     */
    private function check_duplicate_phone($phone) {
        // Normalize phone number (remove spaces, dashes, etc.)
        $normalized_phone = preg_replace('/[^0-9+]/', '', $phone);
        
        $args = array(
            'post_type' => 'form_entries',
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => 'phone-number',
                    'value' => $phone,
                    'compare' => '='
                )
            ),
            'fields' => 'ids',
            'posts_per_page' => 1
        );
        
        $query = new WP_Query($args);
        
        // If exact match found, return true
        if ($query->have_posts()) {
            return true;
        }
        
        // Also check for normalized phone number matches
        $all_entries = get_posts(array(
            'post_type' => 'form_entries',
            'post_status' => 'publish',
            'numberposts' => -1,
            'fields' => 'ids'
        ));
        
        foreach ($all_entries as $entry_id) {
            $existing_phone = get_post_meta($entry_id, 'phone-number', true);
            $normalized_existing = preg_replace('/[^0-9+]/', '', $existing_phone);
            
            if ($normalized_phone === $normalized_existing) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get validation error messages
     */
    public function get_validation_messages() {
        return array(
            'email_duplicate' => __('This email address has already been used for a previous submission. Please use a different email address.', CF7CW_TEXT_DOMAIN),
            'phone_duplicate' => __('This phone number has already been used for a previous submission. Please use a different phone number.', CF7CW_TEXT_DOMAIN),
        );
    }
    
    /**
     * Custom validation for specific form fields
     */
    public function validate_custom_fields($result, $tag) {
        $tag_name = $tag->name;
        $value = isset($_POST[$tag_name]) ? sanitize_text_field($_POST[$tag_name]) : '';
        
        switch ($tag_name) {
            case 'identification-number':
                if (!empty($value) && !$this->validate_identification_number($value)) {
                    $result->invalidate($tag, __('Please enter a valid identification number.', CF7CW_TEXT_DOMAIN));
                }
                break;
                
            case 'passport-number':
                if (!empty($value) && !$this->validate_passport_number($value)) {
                    $result->invalidate($tag, __('Please enter a valid passport number.', CF7CW_TEXT_DOMAIN));
                }
                break;
                
            case 'expiration-date':
                if (!empty($value) && !$this->validate_future_date($value)) {
                    $result->invalidate($tag, __('Expiration date must be in the future.', CF7CW_TEXT_DOMAIN));
                }
                break;
        }
        
        return $result;
    }
    
    /**
     * Validate identification number format
     */
    private function validate_identification_number($id_number) {
        // Basic validation - adjust according to your requirements
        return preg_match('/^[A-Z0-9]{6,20}$/', strtoupper($id_number));
    }
    
    /**
     * Validate passport number format
     */
    private function validate_passport_number($passport) {
        // Basic validation - adjust according to your requirements
        return preg_match('/^[A-Z0-9]{6,15}$/', strtoupper($passport));
    }
    
    /**
     * Validate that date is in the future
     */
    private function validate_future_date($date) {
        $date_obj = DateTime::createFromFormat('Y-m-d', $date);
        if (!$date_obj) {
            return false;
        }
        
        $today = new DateTime();
        return $date_obj > $today;
    }
    
    /**
     * Add custom validation messages to CF7
     */
    public function add_custom_validation_messages($messages) {
        $custom_messages = array(
            'email_duplicate' => array(
                'default' => __('This email address has already been used for a previous submission.', CF7CW_TEXT_DOMAIN)
            ),
            'phone_duplicate' => array(
                'default' => __('This phone number has already been used for a previous submission.', CF7CW_TEXT_DOMAIN)
            ),
            'invalid_identification' => array(
                'default' => __('Please enter a valid identification number.', CF7CW_TEXT_DOMAIN)
            ),
            'invalid_passport' => array(
                'default' => __('Please enter a valid passport number.', CF7CW_TEXT_DOMAIN)
            ),
            'invalid_future_date' => array(
                'default' => __('Date must be in the future.', CF7CW_TEXT_DOMAIN)
            )
        );
        
        return array_merge($messages, $custom_messages);
    }
}

<?php

/**
 * Form Handler - Main form processing logic
 */

if (!defined('ABSPATH')) {
    exit;
}

class CF7CW_Form_Handler
{
    private $current_entry_id = null;
    private $current_login_key = null;
    private $current_qr_url = null;

    public function __construct()
    {
        add_action('wpcf7_before_send_mail', array($this, 'prepare_mail_data'), 10, 3);
        add_action('wpcf7_mail_sent', array($this, 'handle_form_submission'));
        add_filter('wpcf7_mail_tag_replaced', array($this, 'replace_custom_mail_tags'), 10, 4);
        add_filter('wpcf7_special_mail_tags', array($this, 'add_special_mail_tags'), 10, 3);
    }

    /**
     * Add special mail tags for CF7 integration
     */
    public function add_special_mail_tags($output, $name, $html)
    {
        $custom_tags = array(
            'cf7cw_login_key',
            'cf7cw_qr_code_url',
            'cf7cw_login_url',
            'cf7cw_user_id'
        );

        if (in_array($name, $custom_tags)) {
            return $this->get_custom_mail_tag_value($name);
        }

        return $output;
    }

    /**
     * Replace custom mail tags in CF7 emails
     */
    public function replace_custom_mail_tags($replaced, $submitted, $html, $mail_tag)
    {
        $tag_name = $mail_tag->get_tag();

        switch ($tag_name) {
            case 'cf7cw_login_key':
                return $this->get_current_login_key();
            case 'cf7cw_qr_code_url':
                return $this->get_current_qr_url();
            case 'cf7cw_login_url':
                return $this->get_current_login_url();
            case 'cf7cw_user_id':
                return $this->get_current_user_id();
        }

        return $replaced;
    }

    /**
     * Handle form submission after mail is sent
     */
    public function handle_form_submission($contact_form)
    {
        $submission = WPCF7_Submission::get_instance();

        if (!$submission) {
            return;
        }

        $target_form_id = $this->get_target_form_id();

        // Only process our target form
        if ($contact_form->id() != $target_form_id) {
            return;
        }

        $posted_data = $submission->get_posted_data();

        // Validate required fields
        if (!$this->validate_required_fields($posted_data)) {
            error_log('CF7CW: Required fields missing in form submission');
            return;
        }

        // Create form entry
        $entry_id = $this->create_form_entry($posted_data);

        if (!$entry_id) {
            error_log('CF7CW: Failed to create form entry');
            return;
        }

        // Generate login key
        $login_system = new CF7CW_Login_System();
        $login_key = $login_system->generate_login_key($entry_id);

        if (!$login_key) {
            error_log('CF7CW: Failed to generate login key for entry ' . $entry_id);
            return;
        }

        // Send emails
        $this->send_notification_emails($entry_id, $posted_data, $login_key);

        // Log successful processing
        error_log('CF7CW: Successfully processed form submission for entry ' . $entry_id);
    }

    /**
     * Create form entry in custom post type
     */
    private function create_form_entry($data)
    {
        $user_name = isset($data['full-name']) ? sanitize_text_field($data['full-name']) : '';
        $user_email = isset($data['email-address']) ? sanitize_email($data['email-address']) : '';

        // Create post
        $post_data = array(
            'post_title' => $user_name,
            'post_type' => 'form_entries',
            'post_status' => 'publish',
            'post_author' => 1,
            'meta_input' => array(
                '_cf7cw_user_id' => 0, // Will be updated with post ID
                '_cf7cw_submission_date' => current_time('mysql')
            )
        );

        $entry_id = wp_insert_post($post_data);

        if (is_wp_error($entry_id)) {
            return false;
        }

        // Update the user ID to match the post ID
        update_post_meta($entry_id, '_cf7cw_user_id', $entry_id);

        // Save all form fields as meta data
        $this->save_form_meta_data($entry_id, $data);

        // Handle file uploads
        $this->handle_file_uploads($entry_id, $data);

        return $entry_id;
    }

    /**
     * Save form meta data
     */
    private function save_form_meta_data($entry_id, $data)
    {
        $field_mapping = array(
            'full-name' => 'full-name',
            'birth-date' => 'birth-date',
            'gender' => 'gender',
            'phone-number' => 'phone-number',
            'email-address' => 'email-address',
            'address' => 'address',
            'country-residence' => 'country-residence',
            'identification-number' => 'identification-number',
            'issuing-country' => 'issuing-country',
            'expiration-date' => 'expiration-date',
            'id-number' => 'id-number',
            'passport-number' => 'passport-number',
            'place-issue' => 'place-issue',
            'date-issue' => 'date-issue',
            'contact-name' => 'contact-name',
            'contact-relationship' => 'contact-relationship',
            'contact-number' => 'contact-number',
            'contact-email' => 'contact-email',
            'occupation' => 'occupation',
            'employer' => 'employer',
            'professional-aff' => 'professional-aff'
        );

        foreach ($field_mapping as $form_field => $meta_key) {
            if (isset($data[$form_field])) {
                $value = is_array($data[$form_field]) ? implode(', ', $data[$form_field]) : $data[$form_field];
                update_post_meta($entry_id, $meta_key, sanitize_text_field($value));
            }
        }
    }

    /**
     * Handle file uploads
     */
    private function handle_file_uploads($entry_id, $data)
    {
        $file_fields = array('occupation-file', 'signature-file');

        foreach ($file_fields as $field) {
            if (isset($data[$field]) && !empty($data[$field])) {
                $file_path = $data[$field];

                // Move uploaded file to media library
                $attachment_id = $this->move_file_to_media_library($file_path, $entry_id);

                if ($attachment_id) {
                    update_post_meta($entry_id, $field, $attachment_id);
                }
            }
        }
    }

    /**
     * Move uploaded file to media library
     */
    private function move_file_to_media_library($file_path, $entry_id)
    {
        if (!file_exists($file_path)) {
            return false;
        }

        $filename = basename($file_path);
        $upload_dir = wp_upload_dir();

        // Create unique filename
        $new_filename = $entry_id . '_' . time() . '_' . $filename;
        $new_file_path = $upload_dir['path'] . '/' . $new_filename;

        // Copy file to uploads directory
        if (copy($file_path, $new_file_path)) {
            // Create attachment
            $attachment = array(
                'guid' => $upload_dir['url'] . '/' . $new_filename,
                'post_mime_type' => wp_check_filetype($new_filename)['type'],
                'post_title' => preg_replace('/\.[^.]+$/', '', $new_filename),
                'post_content' => '',
                'post_status' => 'inherit'
            );

            $attachment_id = wp_insert_attachment($attachment, $new_file_path, $entry_id);

            if (!is_wp_error($attachment_id)) {
                require_once(ABSPATH . 'wp-admin/includes/image.php');
                $attachment_data = wp_generate_attachment_metadata($attachment_id, $new_file_path);
                wp_update_attachment_metadata($attachment_id, $attachment_data);

                return $attachment_id;
            }
        }

        return false;
    }

    /**
     * Send notification emails
     */
    private function send_notification_emails($entry_id, $data, $login_key)
    {
        $email_handler = new CF7CW_Email_Handler();

        $user_name = isset($data['full-name']) ? $data['full-name'] : '';
        $user_email = isset($data['email-address']) ? $data['email-address'] : '';

        // Send user confirmation email
        if (!empty($user_email)) {
            $email_handler->send_user_confirmation($entry_id, $user_email, $user_name, $login_key);
        }

        // Send admin notification
        $email_handler->send_admin_notification($data);
    }

    /**
     * Validate required fields
     */
    private function validate_required_fields($data)
    {
        $required_fields = array('full-name', 'email-address', 'phone-number');

        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get target form ID
     */
    private function get_target_form_id()
    {
        // You can make this configurable in admin settings
        $settings = get_option('cf7cw_settings', array());

        if (isset($settings['target_form_id'])) {
            return $settings['target_form_id'];
        }

        // Default form ID - update this to match your form
        return '4be63d7';
    }

    /**
     * Get form field labels
     */
    public function get_form_field_labels()
    {
        return array(
            'full-name' => __('Full Name', CF7CW_TEXT_DOMAIN),
            'birth-date' => __('Date of Birth', CF7CW_TEXT_DOMAIN),
            'gender' => __('Gender', CF7CW_TEXT_DOMAIN),
            'phone-number' => __('Phone Number', CF7CW_TEXT_DOMAIN),
            'email-address' => __('Email Address', CF7CW_TEXT_DOMAIN),
            'address' => __('Address', CF7CW_TEXT_DOMAIN),
            'country-residence' => __('Country of Residence', CF7CW_TEXT_DOMAIN),
            'identification-number' => __('Identification Number', CF7CW_TEXT_DOMAIN),
            'issuing-country' => __('Issuing Country', CF7CW_TEXT_DOMAIN),
            'expiration-date' => __('Expiration Date', CF7CW_TEXT_DOMAIN),
            'id-number' => __('National ID Number', CF7CW_TEXT_DOMAIN),
            'passport-number' => __('Passport Number', CF7CW_TEXT_DOMAIN),
            'place-issue' => __('Place of Issue', CF7CW_TEXT_DOMAIN),
            'date-issue' => __('Date of Issue', CF7CW_TEXT_DOMAIN),
            'contact-name' => __('Emergency Contact Name', CF7CW_TEXT_DOMAIN),
            'contact-relationship' => __('Contact Relationship', CF7CW_TEXT_DOMAIN),
            'contact-number' => __('Contact Phone Number', CF7CW_TEXT_DOMAIN),
            'contact-email' => __('Contact Email Address', CF7CW_TEXT_DOMAIN),
            'occupation' => __('Occupation', CF7CW_TEXT_DOMAIN),
            'employer' => __('Employer', CF7CW_TEXT_DOMAIN),
            'professional-aff' => __('Professional Affiliations', CF7CW_TEXT_DOMAIN),
            'occupation-file' => __('Passport Photo', CF7CW_TEXT_DOMAIN),
            'signature-file' => __('Digital Signature', CF7CW_TEXT_DOMAIN),
        );
    }

    /**
     * Prepare mail data before CF7 sends emails
     */
    public function prepare_mail_data($contact_form, $abort, $submission)
    {
        $target_form_id = $this->get_target_form_id();

        if ($contact_form->id() != $target_form_id) {
            return;
        }

        $posted_data = $submission->get_posted_data();

        // Create form entry first
        $entry_id = $this->create_form_entry($posted_data);

        if ($entry_id) {
            // Generate login key
            $login_system = new CF7CW_Login_System();
            $login_key = $login_system->generate_login_key($entry_id);

            // Generate QR code
            $qr_generator = new CF7CW_QR_Generator();
            $qr_result = $qr_generator->generate_login_qr($entry_id, $login_key);

            // Store for mail tag replacement
            $this->current_entry_id = $entry_id;
            $this->current_login_key = $login_key;
            $this->current_qr_url = $qr_result ? $qr_result['url'] : '';
        }
    }

    /**
     * Get custom mail tag value
     */
    public function get_custom_mail_tag_value($tag_name)
    {
        switch ($tag_name) {
            case 'cf7cw_login_key':
                return $this->current_login_key ?: '';
            case 'cf7cw_qr_code_url':
                return $this->current_qr_url ?: '';
            case 'cf7cw_login_url':
                return $this->current_entry_id ? home_url('/form-login/?user_id=' . $this->current_entry_id) : '';
            case 'cf7cw_user_id':
                return $this->current_entry_id ?: '';
            default:
                return '';
        }
    }

    /**
     * Get current login key for mail tags
     */
    public function get_current_login_key()
    {
        return $this->current_login_key ?: '';
    }

    /**
     * Get current QR URL for mail tags
     */
    public function get_current_qr_url()
    {
        return $this->current_qr_url ?: '';
    }

    /**
     * Get current login URL for mail tags
     */
    public function get_current_login_url()
    {
        return $this->current_entry_id ? home_url('/form-login/?user_id=' . $this->current_entry_id) : '';
    }

    /**
     * Get current user ID for mail tags
     */
    public function get_current_user_id()
    {
        return $this->current_entry_id ?: '';
    }

    /**
     * Debug form submission
     */
    public function debug_submission($contact_form)
    {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $submission = WPCF7_Submission::get_instance();

            if ($submission) {
                $data = $submission->get_posted_data();
                error_log('CF7CW Debug - Form ID: ' . $contact_form->id());
                error_log('CF7CW Debug - Posted Data: ' . print_r($data, true));
            }
        }
    }
}

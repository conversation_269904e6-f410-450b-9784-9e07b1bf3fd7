<?php
/**
 * Custom Post Type Handler
 */

if (!defined('ABSPATH')) {
    exit;
}

class CF7CW_CPT {
    
    public function __construct() {
        add_action('init', array($this, 'register_form_entries_cpt'));
        add_filter('manage_form_entries_posts_columns', array($this, 'add_custom_columns'));
        add_action('manage_form_entries_posts_custom_column', array($this, 'populate_custom_columns'), 10, 2);
        add_filter('manage_edit-form_entries_sortable_columns', array($this, 'make_columns_sortable'));
        add_action('add_meta_boxes', array($this, 'add_form_data_meta_box'));
        add_action('save_post', array($this, 'save_form_data_meta_box'));
        add_action('post_updated', array($this, 'handle_admin_edit_notification'), 10, 3);
    }
    
    public function register_form_entries_cpt() {
        $labels = array(
            'name' => __('Form Entries', CF7CW_TEXT_DOMAIN),
            'singular_name' => __('Form Entry', CF7CW_TEXT_DOMAIN),
            'menu_name' => __('Form Entries', CF7CW_TEXT_DOMAIN),
            'add_new' => __('Add New Entry', CF7CW_TEXT_DOMAIN),
            'add_new_item' => __('Add New Form Entry', CF7CW_TEXT_DOMAIN),
            'edit_item' => __('Edit Form Entry', CF7CW_TEXT_DOMAIN),
            'new_item' => __('New Form Entry', CF7CW_TEXT_DOMAIN),
            'view_item' => __('View Form Entry', CF7CW_TEXT_DOMAIN),
            'search_items' => __('Search Form Entries', CF7CW_TEXT_DOMAIN),
            'not_found' => __('No form entries found', CF7CW_TEXT_DOMAIN),
            'not_found_in_trash' => __('No form entries found in trash', CF7CW_TEXT_DOMAIN),
        );
        
        $args = array(
            'labels' => $labels,
            'public' => false,
            'show_ui' => true,
            'show_in_menu' => true,
            'menu_icon' => 'dashicons-forms',
            'menu_position' => 25,
            'capability_type' => 'post',
            'capabilities' => array(
                'create_posts' => 'manage_options',
            ),
            'map_meta_cap' => true,
            'hierarchical' => false,
            'supports' => array('title', 'custom-fields'),
            'has_archive' => false,
            'rewrite' => false,
            'query_var' => false,
        );
        
        register_post_type('form_entries', $args);
    }
    
    public function add_custom_columns($columns) {
        $new_columns = array();
        $new_columns['cb'] = $columns['cb'];
        $new_columns['title'] = __('Customer Name', CF7CW_TEXT_DOMAIN);
        $new_columns['customer_email'] = __('Email Address', CF7CW_TEXT_DOMAIN);
        $new_columns['customer_phone'] = __('Phone Number', CF7CW_TEXT_DOMAIN);
        $new_columns['submission_date'] = __('Submission Date', CF7CW_TEXT_DOMAIN);
        $new_columns['login_key'] = __('Login Key', CF7CW_TEXT_DOMAIN);
        $new_columns['date'] = $columns['date'];
        
        return $new_columns;
    }
    
    public function populate_custom_columns($column, $post_id) {
        switch ($column) {
            case 'customer_email':
                echo esc_html(get_post_meta($post_id, 'email-address', true));
                break;
                
            case 'customer_phone':
                echo esc_html(get_post_meta($post_id, 'phone-number', true));
                break;
                
            case 'submission_date':
                echo get_the_date('Y-m-d H:i:s', $post_id);
                break;
                
            case 'login_key':
                global $wpdb;
                $table = $wpdb->prefix . 'cf7cw_entry_keys';
                $row = $wpdb->get_row($wpdb->prepare("SELECT login_key FROM $table WHERE user_id = %d", $post_id));
                echo $row ? esc_html($row->login_key) : __('N/A', CF7CW_TEXT_DOMAIN);
                break;
        }
    }
    
    public function make_columns_sortable($columns) {
        $columns['customer_email'] = 'customer_email';
        $columns['customer_phone'] = 'customer_phone';
        $columns['submission_date'] = 'submission_date';
        return $columns;
    }
    
    public function add_form_data_meta_box() {
        add_meta_box(
            'form_data_meta_box',
            __('Form Submission Data', CF7CW_TEXT_DOMAIN),
            array($this, 'form_data_meta_box_callback'),
            'form_entries',
            'normal',
            'high'
        );
    }
    
    public function form_data_meta_box_callback($post) {
        wp_nonce_field('form_data_meta_box', 'form_data_meta_box_nonce');
        
        // Get all meta data for this post
        $meta_data = get_post_meta($post->ID);
        
        // Define field labels
        $field_labels = array(
            'full-name' => __('Full Name', CF7CW_TEXT_DOMAIN),
            'birth-date' => __('Date of Birth', CF7CW_TEXT_DOMAIN),
            'gender' => __('Gender', CF7CW_TEXT_DOMAIN),
            'phone-number' => __('Phone Number', CF7CW_TEXT_DOMAIN),
            'email-address' => __('Email Address', CF7CW_TEXT_DOMAIN),
            'address' => __('Address', CF7CW_TEXT_DOMAIN),
            'country-residence' => __('Country of Residence', CF7CW_TEXT_DOMAIN),
            'identification-number' => __('Identification Number', CF7CW_TEXT_DOMAIN),
            'issuing-country' => __('Issuing Country', CF7CW_TEXT_DOMAIN),
            'expiration-date' => __('Expiration Date', CF7CW_TEXT_DOMAIN),
            'id-number' => __('National ID Number', CF7CW_TEXT_DOMAIN),
            'passport-number' => __('Passport Number', CF7CW_TEXT_DOMAIN),
            'place-issue' => __('Place of Issue', CF7CW_TEXT_DOMAIN),
            'date-issue' => __('Date of Issue', CF7CW_TEXT_DOMAIN),
            'contact-name' => __('Emergency Contact Name', CF7CW_TEXT_DOMAIN),
            'contact-relationship' => __('Contact Relationship', CF7CW_TEXT_DOMAIN),
            'contact-number' => __('Contact Phone Number', CF7CW_TEXT_DOMAIN),
            'contact-email' => __('Contact Email Address', CF7CW_TEXT_DOMAIN),
            'occupation' => __('Occupation', CF7CW_TEXT_DOMAIN),
            'employer' => __('Employer', CF7CW_TEXT_DOMAIN),
            'professional-aff' => __('Professional Affiliations', CF7CW_TEXT_DOMAIN),
            'occupation-file' => __('Passport Photo', CF7CW_TEXT_DOMAIN),
            'signature-file' => __('Digital Signature', CF7CW_TEXT_DOMAIN),
        );
        
        echo '<table class="form-table">';
        echo '<tbody>';
        
        foreach ($field_labels as $field_key => $field_label) {
            $value = isset($meta_data[$field_key]) ? $meta_data[$field_key][0] : '';
            
            echo '<tr>';
            echo '<th scope="row"><label for="' . esc_attr($field_key) . '">' . esc_html($field_label) . '</label></th>';
            echo '<td>';
            
            if (in_array($field_key, array('occupation-file', 'signature-file'))) {
                // Handle file fields
                if ($value) {
                    $file_url = wp_get_attachment_url($value);
                    if ($file_url) {
                        echo '<a href="' . esc_url($file_url) . '" target="_blank">' . __('View File', CF7CW_TEXT_DOMAIN) . '</a><br>';
                    }
                }
                echo '<input type="file" id="' . esc_attr($field_key) . '" name="' . esc_attr($field_key) . '" />';
            } else {
                // Handle text fields
                echo '<input type="text" id="' . esc_attr($field_key) . '" name="' . esc_attr($field_key) . '" value="' . esc_attr($value) . '" class="regular-text" />';
            }
            
            echo '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
    }
    
    public function save_form_data_meta_box($post_id) {
        // Check if nonce is valid
        if (!isset($_POST['form_data_meta_box_nonce']) || !wp_verify_nonce($_POST['form_data_meta_box_nonce'], 'form_data_meta_box')) {
            return;
        }
        
        // Check if user has permission to edit
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        // Check if this is an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        // Check post type
        if (get_post_type($post_id) !== 'form_entries') {
            return;
        }
        
        // Store original values for comparison
        $original_data = array();
        $field_labels = array(
            'full-name', 'birth-date', 'gender', 'phone-number', 'email-address',
            'address', 'country-residence', 'identification-number', 'issuing-country',
            'expiration-date', 'id-number', 'passport-number', 'place-issue', 'date-issue',
            'contact-name', 'contact-relationship', 'contact-number', 'contact-email',
            'occupation', 'employer', 'professional-aff'
        );
        
        foreach ($field_labels as $field) {
            $original_data[$field] = get_post_meta($post_id, $field, true);
        }
        
        // Save updated data
        $changed_fields = array();
        foreach ($field_labels as $field) {
            if (isset($_POST[$field])) {
                $new_value = sanitize_text_field($_POST[$field]);
                $old_value = $original_data[$field];
                
                if ($new_value !== $old_value) {
                    $changed_fields[$field] = array(
                        'old' => $old_value,
                        'new' => $new_value
                    );
                }
                
                update_post_meta($post_id, $field, $new_value);
            }
        }
        
        // Store changed fields for email notification
        if (!empty($changed_fields)) {
            update_post_meta($post_id, '_cf7cw_changed_fields', $changed_fields);
        }
    }
    
    public function handle_admin_edit_notification($post_id, $post_after, $post_before) {
        if (get_post_type($post_id) !== 'form_entries') {
            return;
        }
        
        $changed_fields = get_post_meta($post_id, '_cf7cw_changed_fields', true);
        
        if (!empty($changed_fields)) {
            // Send notification email
            $email_handler = new CF7CW_Email_Handler();
            $email_handler->send_admin_edit_notification($post_id, $changed_fields);
            
            // Clean up the temporary meta
            delete_post_meta($post_id, '_cf7cw_changed_fields');
        }
    }
}

<?php
/**
 * Login System Handler
 */

if (!defined('ABSPATH')) {
    exit;
}

class CF7CW_Login_System {
    
    private $session_timeout = 900; // 15 minutes in seconds
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('wp_ajax_cf7cw_login', array($this, 'handle_ajax_login'));
        add_action('wp_ajax_nopriv_cf7cw_login', array($this, 'handle_ajax_login'));
        add_action('wp_ajax_cf7cw_logout', array($this, 'handle_ajax_logout'));
        add_action('wp_ajax_nopriv_cf7cw_logout', array($this, 'handle_ajax_logout'));
        add_shortcode('cf7cw_login_form', array($this, 'render_login_form'));
        add_shortcode('cf7cw_user_submissions', array($this, 'render_user_submissions'));
        
        // Schedule cleanup events
        add_action('cf7cw_cleanup_expired_sessions', array($this, 'cleanup_expired_sessions'));
        add_action('cf7cw_cleanup_expired_keys', array($this, 'cleanup_expired_keys'));
        
        if (!wp_next_scheduled('cf7cw_cleanup_expired_sessions')) {
            wp_schedule_event(time(), 'hourly', 'cf7cw_cleanup_expired_sessions');
        }
        
        if (!wp_next_scheduled('cf7cw_cleanup_expired_keys')) {
            wp_schedule_event(time(), 'daily', 'cf7cw_cleanup_expired_keys');
        }
    }
    
    public function init() {
        // Start session if not already started
        if (!session_id()) {
            session_start();
        }
        
        // Check for session timeout
        $this->check_session_timeout();
    }
    
    /**
     * Generate unique login key
     */
    public function generate_login_key($user_id) {
        global $wpdb;
        
        // Generate a secure random key
        $login_key = $this->generate_secure_key();
        
        // Set expiration time (keys expire after 30 days)
        $expires_at = date('Y-m-d H:i:s', time() + (30 * 24 * 60 * 60));
        
        $table_name = $wpdb->prefix . 'cf7cw_entry_keys';
        
        // Delete existing key for this user
        $wpdb->delete($table_name, array('user_id' => $user_id));
        
        // Insert new key
        $result = $wpdb->insert(
            $table_name,
            array(
                'user_id' => $user_id,
                'login_key' => $login_key,
                'expires_at' => $expires_at,
                'is_active' => 1
            ),
            array('%d', '%s', '%s', '%d')
        );
        
        if ($result) {
            return $login_key;
        }
        
        return false;
    }
    
    /**
     * Generate secure random key
     */
    private function generate_secure_key($length = 32) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $key = '';
        
        for ($i = 0; $i < $length; $i++) {
            $key .= $characters[wp_rand(0, strlen($characters) - 1)];
        }
        
        return $key;
    }
    
    /**
     * Validate login key
     */
    public function validate_login_key($user_id, $login_key) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'cf7cw_entry_keys';
        
        $result = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE user_id = %d AND login_key = %s AND is_active = 1 AND expires_at > NOW()",
            $user_id,
            $login_key
        ));
        
        return $result !== null;
    }
    
    /**
     * Create user session
     */
    public function create_session($user_id) {
        global $wpdb;
        
        $session_token = $this->generate_secure_key(64);
        $expires_at = date('Y-m-d H:i:s', time() + $this->session_timeout);
        
        $table_name = $wpdb->prefix . 'cf7cw_user_sessions';
        
        // Clean up existing sessions for this user
        $wpdb->delete($table_name, array('user_id' => $user_id));
        
        // Create new session
        $result = $wpdb->insert(
            $table_name,
            array(
                'user_id' => $user_id,
                'session_token' => $session_token,
                'expires_at' => $expires_at,
                'ip_address' => $this->get_client_ip(),
                'user_agent' => substr($_SERVER['HTTP_USER_AGENT'], 0, 255)
            ),
            array('%d', '%s', '%s', '%s', '%s')
        );
        
        if ($result) {
            // Set session variables
            $_SESSION['cf7cw_user_id'] = $user_id;
            $_SESSION['cf7cw_session_token'] = $session_token;
            $_SESSION['cf7cw_login_time'] = time();
            
            return $session_token;
        }
        
        return false;
    }
    
    /**
     * Validate session
     */
    public function validate_session() {
        if (!isset($_SESSION['cf7cw_user_id']) || !isset($_SESSION['cf7cw_session_token'])) {
            return false;
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'cf7cw_user_sessions';
        
        $result = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE user_id = %d AND session_token = %s AND expires_at > NOW()",
            $_SESSION['cf7cw_user_id'],
            $_SESSION['cf7cw_session_token']
        ));
        
        return $result !== null;
    }
    
    /**
     * Check session timeout
     */
    private function check_session_timeout() {
        if (isset($_SESSION['cf7cw_login_time'])) {
            $elapsed_time = time() - $_SESSION['cf7cw_login_time'];
            
            if ($elapsed_time > $this->session_timeout) {
                $this->destroy_session();
            }
        }
    }
    
    /**
     * Destroy session
     */
    public function destroy_session() {
        if (isset($_SESSION['cf7cw_user_id']) && isset($_SESSION['cf7cw_session_token'])) {
            global $wpdb;
            $table_name = $wpdb->prefix . 'cf7cw_user_sessions';
            
            $wpdb->delete($table_name, array(
                'user_id' => $_SESSION['cf7cw_user_id'],
                'session_token' => $_SESSION['cf7cw_session_token']
            ));
        }
        
        unset($_SESSION['cf7cw_user_id']);
        unset($_SESSION['cf7cw_session_token']);
        unset($_SESSION['cf7cw_login_time']);
    }
    
    /**
     * Handle AJAX login
     */
    public function handle_ajax_login() {
        check_ajax_referer('cf7cw_login_nonce', 'nonce');
        
        $user_id = intval($_POST['user_id']);
        $login_key = sanitize_text_field($_POST['login_key']);
        
        if ($this->validate_login_key($user_id, $login_key)) {
            $session_token = $this->create_session($user_id);
            
            if ($session_token) {
                wp_send_json_success(array(
                    'message' => __('Login successful!', CF7CW_TEXT_DOMAIN),
                    'redirect' => add_query_arg('user_id', $user_id, get_permalink())
                ));
            } else {
                wp_send_json_error(array(
                    'message' => __('Failed to create session. Please try again.', CF7CW_TEXT_DOMAIN)
                ));
            }
        } else {
            wp_send_json_error(array(
                'message' => __('Invalid login key. Please check your key and try again.', CF7CW_TEXT_DOMAIN)
            ));
        }
    }
    
    /**
     * Handle AJAX logout
     */
    public function handle_ajax_logout() {
        check_ajax_referer('cf7cw_logout_nonce', 'nonce');
        
        $this->destroy_session();
        
        wp_send_json_success(array(
            'message' => __('Logged out successfully.', CF7CW_TEXT_DOMAIN),
            'redirect' => home_url()
        ));
    }
    
    /**
     * Render login form shortcode
     */
    public function render_login_form($atts) {
        $atts = shortcode_atts(array(
            'user_id' => isset($_GET['user_id']) ? intval($_GET['user_id']) : 0
        ), $atts);
        
        // Check if user is already logged in
        if ($this->validate_session()) {
            return $this->render_user_submissions();
        }
        
        ob_start();
        include CF7CW_PLUGIN_PATH . 'templates/login-form.php';
        return ob_get_clean();
    }
    
    /**
     * Render user submissions shortcode
     */
    public function render_user_submissions($atts = array()) {
        if (!$this->validate_session()) {
            return '<p>' . __('Please log in to view your submissions.', CF7CW_TEXT_DOMAIN) . '</p>';
        }
        
        $user_id = $_SESSION['cf7cw_user_id'];
        
        // Get user's form entries
        $entries = get_posts(array(
            'post_type' => 'form_entries',
            'meta_query' => array(
                array(
                    'key' => '_cf7cw_user_id',
                    'value' => $user_id,
                    'compare' => '='
                )
            ),
            'posts_per_page' => -1,
            'orderby' => 'date',
            'order' => 'DESC'
        ));
        
        ob_start();
        include CF7CW_PLUGIN_PATH . 'templates/user-submissions.php';
        return ob_get_clean();
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }
    
    /**
     * Cleanup expired sessions
     */
    public function cleanup_expired_sessions() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'cf7cw_user_sessions';
        
        $wpdb->query("DELETE FROM $table_name WHERE expires_at < NOW()");
    }
    
    /**
     * Cleanup expired keys
     */
    public function cleanup_expired_keys() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'cf7cw_entry_keys';
        
        $wpdb->query("DELETE FROM $table_name WHERE expires_at < NOW()");
    }
}

<?php
/**
 * QR Code Generator
 */

if (!defined('ABSPATH')) {
    exit;
}

class CF7CW_QR_Generator {
    
    private $qr_api_url = 'https://api.qrserver.com/v1/create-qr-code/';
    
    public function __construct() {
        add_action('init', array($this, 'init'));
    }
    
    public function init() {
        // Create uploads directory for QR codes
        $this->create_qr_directory();
    }
    
    /**
     * Generate QR code for user login
     */
    public function generate_login_qr($user_id, $login_key) {
        $login_url = $this->get_login_url($user_id);
        
        // Generate QR code using online service (fallback method)
        $qr_code_data = $this->generate_qr_code_online($login_url);
        
        if ($qr_code_data) {
            // Save QR code to uploads directory
            $file_path = $this->save_qr_code($user_id, $qr_code_data);
            return $file_path;
        }
        
        return false;
    }
    
    /**
     * Generate QR code using online service
     */
    private function generate_qr_code_online($data) {
        $params = array(
            'size' => '200x200',
            'data' => urlencode($data),
            'format' => 'png',
            'ecc' => 'M',
            'margin' => '10'
        );
        
        $url = $this->qr_api_url . '?' . http_build_query($params);
        
        $response = wp_remote_get($url, array(
            'timeout' => 30,
            'headers' => array(
                'User-Agent' => 'CF7 Custom Workflow Plugin'
            )
        ));
        
        if (is_wp_error($response)) {
            error_log('QR Code generation failed: ' . $response->get_error_message());
            return false;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            error_log('QR Code generation failed with response code: ' . $response_code);
            return false;
        }
        
        return wp_remote_retrieve_body($response);
    }
    
    /**
     * Generate QR code using PHP QR Code library (if available)
     */
    private function generate_qr_code_php($data) {
        // Check if QR Code library is available
        if (!class_exists('QRcode')) {
            return false;
        }
        
        $temp_file = tempnam(sys_get_temp_dir(), 'qr_');
        
        try {
            QRcode::png($data, $temp_file, QR_ECLEVEL_M, 8, 2);
            $qr_data = file_get_contents($temp_file);
            unlink($temp_file);
            return $qr_data;
        } catch (Exception $e) {
            error_log('QR Code generation failed: ' . $e->getMessage());
            if (file_exists($temp_file)) {
                unlink($temp_file);
            }
            return false;
        }
    }
    
    /**
     * Save QR code to uploads directory
     */
    private function save_qr_code($user_id, $qr_data) {
        $upload_dir = wp_upload_dir();
        $qr_dir = $upload_dir['basedir'] . '/cf7cw-qr-codes/';
        
        if (!file_exists($qr_dir)) {
            wp_mkdir_p($qr_dir);
        }
        
        $filename = 'qr_' . $user_id . '_' . time() . '.png';
        $file_path = $qr_dir . $filename;
        
        if (file_put_contents($file_path, $qr_data)) {
            return array(
                'path' => $file_path,
                'url' => $upload_dir['baseurl'] . '/cf7cw-qr-codes/' . $filename,
                'filename' => $filename
            );
        }
        
        return false;
    }
    
    /**
     * Get login URL with user ID parameter
     */
    private function get_login_url($user_id) {
        $login_page = get_page_by_path('form-login');
        
        if ($login_page) {
            $login_url = get_permalink($login_page->ID);
        } else {
            $login_url = home_url('/form-login/');
        }
        
        return add_query_arg('user_id', $user_id, $login_url);
    }
    
    /**
     * Create QR codes directory
     */
    private function create_qr_directory() {
        $upload_dir = wp_upload_dir();
        $qr_dir = $upload_dir['basedir'] . '/cf7cw-qr-codes/';
        
        if (!file_exists($qr_dir)) {
            wp_mkdir_p($qr_dir);
            
            // Create .htaccess file to protect directory
            $htaccess_content = "Options -Indexes\n";
            $htaccess_content .= "deny from all\n";
            file_put_contents($qr_dir . '.htaccess', $htaccess_content);
            
            // Create index.php file
            file_put_contents($qr_dir . 'index.php', '<?php // Silence is golden');
        }
    }
    
    /**
     * Get QR code for user
     */
    public function get_user_qr_code($user_id) {
        $upload_dir = wp_upload_dir();
        $qr_dir = $upload_dir['basedir'] . '/cf7cw-qr-codes/';
        
        // Look for existing QR code
        $pattern = $qr_dir . 'qr_' . $user_id . '_*.png';
        $files = glob($pattern);
        
        if (!empty($files)) {
            // Get the most recent file
            $latest_file = array_pop($files);
            $filename = basename($latest_file);
            
            return array(
                'path' => $latest_file,
                'url' => $upload_dir['baseurl'] . '/cf7cw-qr-codes/' . $filename,
                'filename' => $filename
            );
        }
        
        return false;
    }
    
    /**
     * Delete QR code files for user
     */
    public function delete_user_qr_codes($user_id) {
        $upload_dir = wp_upload_dir();
        $qr_dir = $upload_dir['basedir'] . '/cf7cw-qr-codes/';
        
        $pattern = $qr_dir . 'qr_' . $user_id . '_*.png';
        $files = glob($pattern);
        
        foreach ($files as $file) {
            if (file_exists($file)) {
                unlink($file);
            }
        }
    }
    
    /**
     * Clean up old QR codes (older than 30 days)
     */
    public function cleanup_old_qr_codes() {
        $upload_dir = wp_upload_dir();
        $qr_dir = $upload_dir['basedir'] . '/cf7cw-qr-codes/';
        
        if (!is_dir($qr_dir)) {
            return;
        }
        
        $files = glob($qr_dir . 'qr_*.png');
        $thirty_days_ago = time() - (30 * 24 * 60 * 60);
        
        foreach ($files as $file) {
            if (filemtime($file) < $thirty_days_ago) {
                unlink($file);
            }
        }
    }
    
    /**
     * Generate QR code with custom styling
     */
    public function generate_styled_qr($data, $options = array()) {
        $default_options = array(
            'size' => '200x200',
            'format' => 'png',
            'ecc' => 'M',
            'margin' => '10',
            'color' => '000000',
            'bgcolor' => 'ffffff'
        );
        
        $options = wp_parse_args($options, $default_options);
        
        $params = array(
            'size' => $options['size'],
            'data' => urlencode($data),
            'format' => $options['format'],
            'ecc' => $options['ecc'],
            'margin' => $options['margin'],
            'color' => $options['color'],
            'bgcolor' => $options['bgcolor']
        );
        
        $url = $this->qr_api_url . '?' . http_build_query($params);
        
        $response = wp_remote_get($url, array(
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        return wp_remote_retrieve_body($response);
    }
    
    /**
     * Validate QR code data
     */
    public function validate_qr_data($data) {
        // Check if data is a valid URL
        if (filter_var($data, FILTER_VALIDATE_URL)) {
            return true;
        }
        
        // Check if data length is reasonable
        if (strlen($data) > 2000) {
            return false;
        }
        
        return true;
    }
}

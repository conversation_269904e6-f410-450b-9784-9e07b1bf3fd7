<?php return array(
    'root' => array(
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => NULL,
        'name' => 'cf7-custom-workflow/qr-generator',
        'dev' => false,
    ),
    'versions' => array(
        'bacon/bacon-qr-code' => array(
            'pretty_version' => '2.0.8',
            'version' => '2.0.8.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../bacon/bacon-qr-code',
            'aliases' => array(),
            'reference' => '8674e51bb65af933a5ffaf1c308a660387c35c22',
            'dev_requirement' => false,
        ),
        'cf7-custom-workflow/qr-generator' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => NULL,
            'dev_requirement' => false,
        ),
        'dasprid/enum' => array(
            'pretty_version' => '1.0.6',
            'version' => '1.0.6.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dasprid/enum',
            'aliases' => array(),
            'reference' => '8dfd07c6d2cf31c8da90c53b83c026c7696dda90',
            'dev_requirement' => false,
        ),
        'endroid/qr-code' => array(
            'pretty_version' => '4.8.5',
            'version' => '4.8.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../endroid/qr-code',
            'aliases' => array(),
            'reference' => '0db25b506a8411a5e1644ebaa67123a6eb7b6a77',
            'dev_requirement' => false,
        ),
    ),
);

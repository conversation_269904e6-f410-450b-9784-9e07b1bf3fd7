# CF7 Custom Workflow Plugin

A comprehensive WordPress plugin that enhances Contact Form 7 with advanced features including duplicate prevention, QR code authentication, secure user access, and custom post type management.

## Features

### Core Functionality
- **Duplicate Entry Prevention**: Prevents duplicate submissions based on email and phone number
- **Custom Post Type**: Saves form submissions as custom post entries with full admin interface
- **QR Code Generation**: Creates unique QR codes for user authentication and access
- **Secure Login System**: Key-based authentication with 15-minute session timeouts
- **Email Templates**: Customizable HTML email templates with QR code integration
- **Security Features**: Screenshot prevention, copy protection, and session management

### Admin Features
- **Settings Management**: Configure form targets, timeouts, and email settings
- **Email Template Editor**: Customize user confirmation and admin notification emails
- **Security Logs**: Monitor access attempts and security events
- **Statistics Dashboard**: View plugin usage statistics and metrics
- **Export Functionality**: Export form entries to CSV format

### Security Features
- **Session Management**: Automatic 15-minute session timeouts
- **Screenshot Prevention**: Disables screenshots, right-click, and developer tools
- **Copy Protection**: Prevents text selection and content copying
- **Print Protection**: Blocks printing of secure pages
- **Developer Tools Detection**: Detects and blocks developer console access

## Installation

1. Upload the plugin files to `/wp-content/plugins/cf7-custom-workflow/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Configure the plugin settings under 'CF7 Workflow' in the admin menu
4. Select your target Contact Form 7 form in the settings

## Requirements

- WordPress 5.0 or higher
- Contact Form 7 plugin (required)
- PHP 7.4 or higher
- MySQL 5.6 or higher

## Configuration

### Initial Setup

1. **Install Contact Form 7**: This plugin requires Contact Form 7 to be installed and activated
2. **Configure Settings**: Go to 'CF7 Workflow' > 'Settings' in your WordPress admin
3. **Select Target Form**: Choose which Contact Form 7 form should use the custom workflow
4. **Enable Features**: Toggle the features you want to use (duplicate checking, QR codes, security)

### Form Setup

Your Contact Form 7 form should include these field names for full functionality:

```
[text* full-name placeholder "Full Name"]
[email* email-address placeholder "Email Address"]
[tel* phone-number placeholder "Phone Number"]
[date birth-date placeholder "Date of Birth"]
[select gender "Male" "Female" "Other"]
[textarea address placeholder "Address"]
[text country-residence placeholder "Country of Residence"]
[text identification-number placeholder "ID Number"]
[text issuing-country placeholder "Issuing Country"]
[date expiration-date placeholder "Expiration Date"]
[text id-number placeholder "National ID"]
[text passport-number placeholder "Passport Number"]
[text place-issue placeholder "Place of Issue"]
[date date-issue placeholder "Date of Issue"]
[text contact-name placeholder "Emergency Contact Name"]
[text contact-relationship placeholder "Relationship"]
[tel contact-number placeholder "Contact Phone"]
[email contact-email placeholder "Contact Email"]
[text occupation placeholder "Occupation"]
[text employer placeholder "Employer"]
[textarea professional-aff placeholder "Professional Affiliations"]
[file occupation-file filetypes:jpg|jpeg|png|pdf limit:2mb]
[file signature-file filetypes:jpg|jpeg|png|pdf limit:2mb]
```

### Login Page

The plugin automatically creates a login page at `/form-login/` with the shortcode `[cf7cw_login_form]`.

Users can also view their submissions using the shortcode `[cf7cw_user_submissions]`.

## Email Templates

The plugin includes three customizable email templates:

1. **User Confirmation**: Sent to users after form submission with login credentials
2. **Admin Notification**: Sent to admin when new submissions are received
3. **Admin Edit Notification**: Sent to users when admin edits their submissions

### Available Placeholders

- `[USER_NAME]` - User's full name
- `[LOGIN_KEY]` - Generated login key
- `[QR_URL]` - Login URL with user ID
- `[SITE_NAME]` - WordPress site name
- `[SITE_URL]` - Site URL
- `[USER_EMAIL]` - User's email address
- `[USER_PHONE]` - User's phone number
- `[SUBMISSION_DATE]` - Date of submission
- `[ADMIN_URL]` - Admin URL for viewing entries
- `[CHANGES_SUMMARY]` - Summary of changes (edit notifications only)

## Database Tables

The plugin creates two custom database tables:

### cf7cw_entry_keys
Stores login keys for user authentication:
- `id` - Primary key
- `user_id` - Reference to form entry post ID
- `login_key` - Unique login key
- `created_at` - Creation timestamp
- `expires_at` - Expiration timestamp
- `is_active` - Active status

### cf7cw_user_sessions
Manages user sessions:
- `id` - Primary key
- `user_id` - Reference to form entry post ID
- `session_token` - Unique session token
- `created_at` - Creation timestamp
- `expires_at` - Expiration timestamp
- `ip_address` - User's IP address
- `user_agent` - User's browser information

## Security Implementation

### Session Security
- 15-minute automatic timeout
- IP address validation
- User agent verification
- Secure token generation

### Content Protection
- Disabled right-click context menu
- Blocked keyboard shortcuts (F12, Ctrl+U, Ctrl+S, etc.)
- Prevented text selection and copying
- Disabled image dragging and saving
- Print protection
- Developer tools detection

### Access Control
- Unique login keys per user
- Session-based authentication
- Automatic cleanup of expired sessions
- Security event logging

## Hooks and Filters

### Actions
- `cf7cw_after_form_submission` - Fired after successful form processing
- `cf7cw_before_email_send` - Fired before sending emails
- `cf7cw_session_expired` - Fired when session expires
- `cf7cw_security_violation` - Fired on security violations

### Filters
- `cf7cw_email_template` - Modify email templates
- `cf7cw_login_key_length` - Change login key length
- `cf7cw_session_timeout` - Modify session timeout
- `cf7cw_qr_code_size` - Change QR code dimensions

## Troubleshooting

### Common Issues

1. **Contact Form 7 Not Found**: Ensure CF7 is installed and activated
2. **QR Codes Not Generating**: Check internet connection for online QR service
3. **Emails Not Sending**: Verify WordPress mail configuration
4. **Sessions Expiring Too Quickly**: Check session timeout settings
5. **Security Features Not Working**: Ensure JavaScript is enabled

### Debug Mode

Enable WordPress debug mode to see detailed error logs:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

Check `/wp-content/debug.log` for CF7CW-related errors.

## Support

For support and bug reports, please check the plugin documentation or contact the development team.

## Changelog

### Version 1.0.0
- Initial release
- Complete form workflow implementation
- QR code generation and authentication
- Security features and session management
- Admin interface and email templates
- Custom post type integration

## License

This plugin is proprietary software developed for specific client requirements.

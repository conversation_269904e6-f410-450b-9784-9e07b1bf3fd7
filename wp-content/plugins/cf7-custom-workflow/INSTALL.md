# Installation Guide for CF7 Custom Workflow

## QR Code Generation Setup

The plugin supports two methods for QR code generation:

### Method 1: Online QR Service (Default - No Setup Required)
- Uses `qrserver.com` API
- Works out of the box
- Requires internet connection
- **No additional setup needed**

### Method 2: Local QR Generation with Composer (Recommended for Production)

For better reliability and offline capability, install the Composer package:

#### Step 1: Install Composer Dependencies

Navigate to the plugin directory and run:

```bash
cd wp-content/plugins/cf7-custom-workflow/
composer install
```

This will install the `endroid/qr-code` package in the `vendor/` directory.

#### Step 2: Verify Installation

After running `composer install`, you should see:
```
wp-content/plugins/cf7-custom-workflow/
├── vendor/
│   ├── autoload.php
│   ├── endroid/
│   └── ...
└── composer.lock
```

#### Step 3: Test QR Generation

The plugin will automatically detect the Composer package and use it as the primary method, falling back to the online service if needed.

## Email Template Integration with Contact Form 7

### Current Setup

Since you're using Contact Form 7's Mail 2 section for user emails, the plugin now integrates with CF7's existing email system by providing **custom mail tags**.

### Available Mail Tags for CF7 Templates

You can use these tags in your Contact Form 7 Mail 2 template:

- `[cf7cw_login_key]` - The generated login key
- `[cf7cw_qr_code_url]` - URL to the QR code image
- `[cf7cw_login_url]` - Direct login URL with user ID
- `[cf7cw_user_id]` - The user's entry ID

### Example CF7 Mail 2 Template

```html
Subject: Your Login Credentials - [your-subject]

Dear [full-name],

Thank you for your submission. Here are your login credentials:

User ID: [cf7cw_user_id]
Login Key: [cf7cw_login_key]

You can access your information using:
- Login URL: [cf7cw_login_url]
- QR Code: [cf7cw_qr_code_url]

Please keep these credentials secure.

Best regards,
[_site_title]
```

### How It Works

1. **Form Submission**: User submits the CF7 form
2. **Data Processing**: Plugin creates entry and generates login credentials
3. **Mail Tag Replacement**: CF7 replaces custom tags with actual values
4. **Email Sent**: CF7 sends the email with login credentials
5. **Post Processing**: Plugin handles any additional tasks

## Plugin Configuration

### Step 1: Basic Settings

1. Go to **CF7 Workflow > Settings** in WordPress admin
2. Select your target Contact Form 7 form (ID: 4be63d7)
3. Enable desired features:
   - ✅ Duplicate entry prevention
   - ✅ QR code generation
   - ✅ Security features

### Step 2: Email Configuration

1. Configure your **From Name** and **From Email**
2. Set **Admin Email** for notifications
3. Adjust **Session Timeout** (default: 900 seconds = 15 minutes)

### Step 3: Contact Form 7 Setup

1. Edit your Contact Form 7 form (ID: 4be63d7)
2. Go to the **Mail (2)** tab
3. Add the custom mail tags to your template:
   ```
   Login Key: [cf7cw_login_key]
   QR Code: [cf7cw_qr_code_url]
   ```

## Testing the Setup

### Test QR Code Generation

1. Submit a test form
2. Check the uploads directory: `wp-content/uploads/cf7cw-qr-codes/`
3. Verify QR code images are created

### Test Email Integration

1. Submit a test form
2. Check that Mail 2 is sent with login credentials
3. Verify custom mail tags are replaced with actual values

### Test Login System

1. Use the generated User ID and Login Key
2. Go to `/form-login/` page
3. Enter credentials and verify access

## Troubleshooting

### QR Codes Not Generating

1. **Check internet connection** (for online method)
2. **Verify Composer installation** (for local method)
3. **Check uploads directory permissions**
4. **Review error logs** in `wp-content/debug.log`

### Mail Tags Not Working

1. **Verify form ID** matches target form in settings
2. **Check CF7 Mail 2 is enabled**
3. **Ensure proper mail tag syntax**: `[cf7cw_login_key]`

### Login Issues

1. **Check session timeout settings**
2. **Verify login page exists** at `/form-login/`
3. **Check database tables** are created properly

## File Permissions

Ensure these directories are writable:
```bash
chmod 755 wp-content/uploads/
chmod 755 wp-content/uploads/cf7cw-qr-codes/
```

## Security Considerations

- QR codes are stored in uploads directory with direct access
- Login keys expire based on settings
- Sessions timeout after 15 minutes by default
- Security features prevent screenshots and copying

## Support

For issues:
1. Enable WordPress debug mode
2. Check `wp-content/debug.log` for CF7CW errors
3. Verify all requirements are met
4. Test with a simple form submission

# CF7 Custom Workflow - Complete Usage Guide

## 🚀 Quick Start (Ready to Upload!)

The plugin is now **complete with Composer dependencies included**. You can simply upload the entire plugin folder to your WordPress site.

### ✅ What's Included
- ✅ All PHP classes and functionality
- ✅ Composer dependencies (endroid/qr-code) pre-installed
- ✅ CSS and JavaScript assets
- ✅ Email templates and admin interface
- ✅ Complete database schema

## 📋 Installation Steps

### Step 1: Upload Plugin
```bash
# Upload the entire folder to:
wp-content/plugins/cf7-custom-workflow/
```

### Step 2: Activate Plugin
1. Go to **WordPress Admin > Plugins**
2. Find "CF7 Custom Workflow"
3. Click **Activate**

### Step 3: Configure Settings
1. Go to **CF7 Workflow > Settings**
2. Select your Contact Form 7 form (ID: 4be63d7)
3. Configure basic settings

## 🎯 Contact Form 7 Integration

### Your Current Form (ID: 4be63d7)
The plugin will automatically work with your existing form. Make sure your form includes these field names:

**Required Fields:**
- `[text* full-name]` - Full Name
- `[email* email-address]` - Email Address  
- `[tel* phone-number]` - Phone Number

**Optional Fields:**
- `[date birth-date]` - Date of Birth
- `[select gender]` - Gender
- `[textarea address]` - Address
- `[text country-residence]` - Country
- `[file occupation-file]` - File Upload
- `[file signature-file]` - Signature File

### Mail 2 Setup (User Email)

In your Contact Form 7 **Mail (2)** tab, you can now use these special tags:

```html
Subject: Your Login Credentials

Dear [full-name],

Thank you for your submission. Here are your login credentials:

🆔 User ID: [cf7cw_user_id]
🔑 Login Key: [cf7cw_login_key]

🌐 Login URL: [cf7cw_login_url]
📱 QR Code: [cf7cw_qr_code_url]

⚠️ Important:
- Keep these credentials secure
- Session expires in 15 minutes
- Screenshots are disabled for security

Best regards,
[_site_title]
```

## 🔧 Plugin Configuration

### Settings Page: CF7 Workflow > Settings

**Basic Configuration:**
- **Target Form**: Select your CF7 form (4be63d7)
- **Enable Features**:
  - ✅ Duplicate entry prevention
  - ✅ QR code generation  
  - ✅ Security features

**Email Settings:**
- **Admin Email**: Where to send notifications
- **From Name**: Your site name
- **From Email**: Your site email

**Security Settings:**
- **Session Timeout**: 900 seconds (15 minutes)
- **Key Expiry**: 30 days

## 🎨 Available Mail Tags for CF7

Use these in your Contact Form 7 Mail (2) template:

| Tag | Description | Example Output |
|-----|-------------|----------------|
| `[cf7cw_user_id]` | Entry ID | `123` |
| `[cf7cw_login_key]` | Login Key | `ABC123XYZ789` |
| `[cf7cw_login_url]` | Direct Login URL | `https://yoursite.com/form-login/?user_id=123` |
| `[cf7cw_qr_code_url]` | QR Code Image | `https://yoursite.com/wp-content/uploads/cf7cw-qr-codes/qr_123.png` |

## 🔐 User Login Process

### Login Page: `/form-login/`
The plugin automatically creates this page with the login form.

**User Experience:**
1. User receives email with credentials
2. User goes to `/form-login/`
3. User enters **User ID** and **Login Key**
4. User gets access to their submission data
5. Session expires after 15 minutes

### QR Code Login
Users can also scan the QR code which contains the login URL with their User ID.

## 🛡️ Security Features

### Automatic Protection
When users access their data, the following security measures activate:

- **Screenshot Prevention**: Disables screenshot functionality
- **Copy Protection**: Prevents text selection and copying
- **Print Protection**: Blocks printing of secure pages
- **Developer Tools**: Detects and blocks F12, Ctrl+U, etc.
- **Session Management**: 15-minute automatic timeout
- **IP Validation**: Sessions tied to IP addresses

### Security Notices
Users will see security warnings about:
- Screenshots being disabled
- Session timeout countdown
- Copy protection active

## 📊 Admin Features

### View Form Entries
- **Location**: WordPress Admin > Form Entries
- **Features**: View all submissions in a table format
- **Details**: Click any entry to see full submission data

### Statistics Dashboard
- **Location**: CF7 Workflow > Settings
- **Metrics**: 
  - Total form entries
  - Active login keys
  - Active sessions
  - Recent entries

### Email Templates
- **Location**: CF7 Workflow > Email Templates
- **Customize**: Admin notification templates
- **Test**: Send test emails

### Security Logs
- **Location**: CF7 Workflow > Security Logs
- **Monitor**: Login attempts, security violations
- **Track**: User access patterns

## 🔄 Workflow Process

### Complete User Journey

1. **Form Submission**
   - User fills out your CF7 form
   - Plugin validates for duplicates
   - Entry saved as Custom Post Type

2. **Credential Generation**
   - Unique User ID assigned
   - Login Key generated
   - QR Code created and saved

3. **Email Delivery**
   - CF7 Mail (2) sent with credentials
   - Custom mail tags replaced with actual values
   - Admin notification sent

4. **User Access**
   - User visits `/form-login/`
   - Enters credentials or scans QR code
   - Secure session created

5. **Data Viewing**
   - User sees their submission data
   - Security features active
   - Session timeout countdown

6. **Session Management**
   - 15-minute automatic timeout
   - Secure logout process
   - Session cleanup

## 🎯 Testing the Setup

### Test Form Submission
1. Submit your CF7 form with test data
2. Check that Mail (2) is sent with login credentials
3. Verify QR code is generated in uploads folder
4. Test login with provided credentials

### Test Security Features
1. Login with test credentials
2. Try to take screenshot (should be blocked)
3. Try to copy text (should be prevented)
4. Wait 15 minutes (session should expire)

### Test Admin Features
1. Check Form Entries in WordPress admin
2. View statistics dashboard
3. Check security logs for access attempts

## 📁 File Structure (Ready to Upload)

```
wp-content/plugins/cf7-custom-workflow/
├── cf7-custom-workflow.php (Main plugin file)
├── composer.json (Composer configuration)
├── composer.lock (Dependency lock file)
├── vendor/ (Composer dependencies - INCLUDED!)
│   ├── autoload.php
│   ├── endroid/qr-code/
│   └── ...
├── includes/ (All PHP classes)
├── templates/ (Email and form templates)
├── assets/ (CSS and JavaScript)
├── README.md (Documentation)
├── USAGE-GUIDE.md (This file)
└── INSTALL.md (Installation guide)
```

## 🚨 Important Notes

### Form Field Names
Make sure your CF7 form uses the exact field names the plugin expects:
- `full-name` (not `fullname` or `full_name`)
- `email-address` (not `email` or `email_address`)
- `phone-number` (not `phone` or `phone_number`)

### Mail 2 Configuration
- Enable Mail (2) in your CF7 form
- Add the custom mail tags to your template
- Test with a real email address

### File Permissions
Ensure uploads directory is writable:
```bash
chmod 755 wp-content/uploads/
```

### Debug Mode
For troubleshooting, enable WordPress debug:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## 🎉 You're Ready!

The plugin is now complete and ready to use. Simply upload the folder and activate it in WordPress. All Composer dependencies are included, so no additional setup is required!

For support, check the debug logs and ensure all requirements are met.

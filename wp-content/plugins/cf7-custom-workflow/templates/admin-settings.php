<?php
/**
 * Admin Settings Template
 */

if (!defined('ABSPATH')) {
    exit;
}

$current_settings = wp_parse_args($settings, array(
    'target_form_id' => '',
    'enable_duplicate_check' => 1,
    'enable_qr_codes' => 1,
    'enable_security_features' => 1,
    'session_timeout' => 900,
    'key_expiry_days' => 30,
    'admin_email' => get_option('admin_email'),
    'from_name' => get_bloginfo('name'),
    'from_email' => get_option('admin_email')
));
?>

<div class="wrap">
    <h1><?php _e('CF7 Custom Workflow Settings', CF7CW_TEXT_DOMAIN); ?></h1>
    
    <form id="cf7cw-settings-form" method="post">
        <?php wp_nonce_field('cf7cw_admin_nonce', 'cf7cw_admin_nonce'); ?>
        
        <table class="form-table">
            <tbody>
                <tr>
                    <th scope="row">
                        <label for="target_form_id"><?php _e('Target Contact Form', CF7CW_TEXT_DOMAIN); ?></label>
                    </th>
                    <td>
                        <select name="target_form_id" id="target_form_id" class="regular-text">
                            <option value=""><?php _e('Select a form...', CF7CW_TEXT_DOMAIN); ?></option>
                            <?php foreach ($cf7_forms as $form): ?>
                                <option value="<?php echo esc_attr($form->ID); ?>" 
                                        <?php selected($current_settings['target_form_id'], $form->ID); ?>>
                                    <?php echo esc_html($form->post_title); ?> (ID: <?php echo $form->ID; ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description">
                            <?php _e('Select the Contact Form 7 form that should use the custom workflow.', CF7CW_TEXT_DOMAIN); ?>
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row"><?php _e('Features', CF7CW_TEXT_DOMAIN); ?></th>
                    <td>
                        <fieldset>
                            <label>
                                <input type="checkbox" name="enable_duplicate_check" value="1" 
                                       <?php checked($current_settings['enable_duplicate_check'], 1); ?>>
                                <?php _e('Enable duplicate entry prevention', CF7CW_TEXT_DOMAIN); ?>
                            </label>
                            <br><br>
                            
                            <label>
                                <input type="checkbox" name="enable_qr_codes" value="1" 
                                       <?php checked($current_settings['enable_qr_codes'], 1); ?>>
                                <?php _e('Enable QR code generation', CF7CW_TEXT_DOMAIN); ?>
                            </label>
                            <br><br>
                            
                            <label>
                                <input type="checkbox" name="enable_security_features" value="1" 
                                       <?php checked($current_settings['enable_security_features'], 1); ?>>
                                <?php _e('Enable security features (screenshot prevention, etc.)', CF7CW_TEXT_DOMAIN); ?>
                            </label>
                        </fieldset>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="session_timeout"><?php _e('Session Timeout', CF7CW_TEXT_DOMAIN); ?></label>
                    </th>
                    <td>
                        <input type="number" name="session_timeout" id="session_timeout" 
                               value="<?php echo esc_attr($current_settings['session_timeout']); ?>" 
                               min="300" max="3600" class="small-text">
                        <span><?php _e('seconds', CF7CW_TEXT_DOMAIN); ?></span>
                        <p class="description">
                            <?php _e('How long user sessions should last (300-3600 seconds). Default: 900 (15 minutes)', CF7CW_TEXT_DOMAIN); ?>
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="key_expiry_days"><?php _e('Login Key Expiry', CF7CW_TEXT_DOMAIN); ?></label>
                    </th>
                    <td>
                        <input type="number" name="key_expiry_days" id="key_expiry_days" 
                               value="<?php echo esc_attr($current_settings['key_expiry_days']); ?>" 
                               min="1" max="365" class="small-text">
                        <span><?php _e('days', CF7CW_TEXT_DOMAIN); ?></span>
                        <p class="description">
                            <?php _e('How long login keys should remain valid (1-365 days). Default: 30', CF7CW_TEXT_DOMAIN); ?>
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="admin_email"><?php _e('Admin Email', CF7CW_TEXT_DOMAIN); ?></label>
                    </th>
                    <td>
                        <input type="email" name="admin_email" id="admin_email" 
                               value="<?php echo esc_attr($current_settings['admin_email']); ?>" 
                               class="regular-text">
                        <p class="description">
                            <?php _e('Email address to receive admin notifications.', CF7CW_TEXT_DOMAIN); ?>
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="from_name"><?php _e('Email From Name', CF7CW_TEXT_DOMAIN); ?></label>
                    </th>
                    <td>
                        <input type="text" name="from_name" id="from_name" 
                               value="<?php echo esc_attr($current_settings['from_name']); ?>" 
                               class="regular-text">
                        <p class="description">
                            <?php _e('Name to appear in the "From" field of emails.', CF7CW_TEXT_DOMAIN); ?>
                        </p>
                    </td>
                </tr>
                
                <tr>
                    <th scope="row">
                        <label for="from_email"><?php _e('Email From Address', CF7CW_TEXT_DOMAIN); ?></label>
                    </th>
                    <td>
                        <input type="email" name="from_email" id="from_email" 
                               value="<?php echo esc_attr($current_settings['from_email']); ?>" 
                               class="regular-text">
                        <p class="description">
                            <?php _e('Email address to appear in the "From" field of emails.', CF7CW_TEXT_DOMAIN); ?>
                        </p>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <?php submit_button(__('Save Settings', CF7CW_TEXT_DOMAIN), 'primary', 'submit', false); ?>
    </form>
    
    <hr>
    
    <h2><?php _e('Plugin Statistics', CF7CW_TEXT_DOMAIN); ?></h2>
    
    <?php
    $admin = new CF7CW_Admin();
    $stats = $admin->get_plugin_stats();
    ?>
    
    <div class="cf7cw-stats-grid">
        <div class="cf7cw-stat-card">
            <h3><?php echo number_format($stats['total_entries']); ?></h3>
            <p><?php _e('Total Form Entries', CF7CW_TEXT_DOMAIN); ?></p>
        </div>
        
        <div class="cf7cw-stat-card">
            <h3><?php echo number_format($stats['active_keys']); ?></h3>
            <p><?php _e('Active Login Keys', CF7CW_TEXT_DOMAIN); ?></p>
        </div>
        
        <div class="cf7cw-stat-card">
            <h3><?php echo number_format($stats['active_sessions']); ?></h3>
            <p><?php _e('Active Sessions', CF7CW_TEXT_DOMAIN); ?></p>
        </div>
        
        <div class="cf7cw-stat-card">
            <h3><?php echo number_format($stats['recent_entries']); ?></h3>
            <p><?php _e('Entries This Week', CF7CW_TEXT_DOMAIN); ?></p>
        </div>
    </div>
    
    <hr>
    
    <h2><?php _e('Quick Actions', CF7CW_TEXT_DOMAIN); ?></h2>
    
    <div class="cf7cw-actions">
        <a href="<?php echo admin_url('edit.php?post_type=form_entries'); ?>" class="button">
            <?php _e('View Form Entries', CF7CW_TEXT_DOMAIN); ?>
        </a>
        
        <a href="<?php echo admin_url('admin.php?page=cf7cw-email-templates'); ?>" class="button">
            <?php _e('Manage Email Templates', CF7CW_TEXT_DOMAIN); ?>
        </a>
        
        <a href="<?php echo admin_url('admin.php?page=cf7cw-security-logs'); ?>" class="button">
            <?php _e('View Security Logs', CF7CW_TEXT_DOMAIN); ?>
        </a>
        
        <a href="<?php echo admin_url('admin.php?page=cf7cw-settings&action=export'); ?>" class="button">
            <?php _e('Export Entries', CF7CW_TEXT_DOMAIN); ?>
        </a>
    </div>
</div>

<style>
.cf7cw-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.cf7cw-stat-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.cf7cw-stat-card h3 {
    font-size: 2.5em;
    margin: 0 0 10px 0;
    color: #0073aa;
}

.cf7cw-stat-card p {
    margin: 0;
    color: #666;
    font-weight: 500;
}

.cf7cw-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.cf7cw-actions .button {
    margin-right: 0;
}
</style>

<script>
jQuery(document).ready(function($) {
    $('#cf7cw-settings-form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $submit = $form.find('input[type="submit"]');
        
        $submit.prop('disabled', true).val('<?php _e('Saving...', CF7CW_TEXT_DOMAIN); ?>');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: $form.serialize() + '&action=cf7cw_save_settings',
            success: function(response) {
                if (response.success) {
                    // Show success message
                    $('<div class="notice notice-success is-dismissible"><p>' + 
                      '<?php _e('Settings saved successfully!', CF7CW_TEXT_DOMAIN); ?>' + 
                      '</p></div>').insertAfter('.wrap h1');
                } else {
                    // Show error message
                    $('<div class="notice notice-error is-dismissible"><p>' + 
                      (response.data || '<?php _e('An error occurred while saving settings.', CF7CW_TEXT_DOMAIN); ?>') + 
                      '</p></div>').insertAfter('.wrap h1');
                }
                
                $submit.prop('disabled', false).val('<?php _e('Save Settings', CF7CW_TEXT_DOMAIN); ?>');
                
                // Auto-dismiss notices after 3 seconds
                setTimeout(function() {
                    $('.notice.is-dismissible').fadeOut();
                }, 3000);
            },
            error: function() {
                $('<div class="notice notice-error is-dismissible"><p>' + 
                  '<?php _e('An error occurred while saving settings.', CF7CW_TEXT_DOMAIN); ?>' + 
                  '</p></div>').insertAfter('.wrap h1');
                
                $submit.prop('disabled', false).val('<?php _e('Save Settings', CF7CW_TEXT_DOMAIN); ?>');
            }
        });
    });
});
</script>

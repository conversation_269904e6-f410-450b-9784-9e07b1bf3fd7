<?php
/**
 * User Submissions Template
 */

if (!defined('ABSPATH')) {
    exit;
}

$form_handler = new CF7CW_Form_Handler();
$field_labels = $form_handler->get_form_field_labels();
?>

<div class="cf7cw-submissions-container">
    <div class="cf7cw-submissions-header">
        <h2><?php _e('Your Submissions', CF7CW_TEXT_DOMAIN); ?></h2>
        <div class="cf7cw-session-info">
            <span class="cf7cw-session-timer" id="cf7cw-session-timer"></span>
            <button type="button" id="cf7cw-logout-btn" class="cf7cw-logout-btn">
                <?php _e('Logout', CF7CW_TEXT_DOMAIN); ?>
            </button>
        </div>
    </div>

    <?php if (empty($entries)): ?>
        <div class="cf7cw-no-submissions">
            <p><?php _e('No submissions found.', CF7CW_TEXT_DOMAIN); ?></p>
        </div>
    <?php else: ?>
        <?php foreach ($entries as $entry): ?>
            <div class="cf7cw-submission-card">
                <div class="cf7cw-submission-header">
                    <h3><?php echo esc_html(get_the_title($entry->ID)); ?></h3>
                    <span class="cf7cw-submission-date">
                        <?php echo get_the_date('F j, Y g:i A', $entry->ID); ?>
                    </span>
                </div>

                <div class="cf7cw-submission-content">
                    <div class="cf7cw-submission-grid">
                        <!-- Personal Information -->
                        <div class="cf7cw-section">
                            <h4><?php _e('Personal Information', CF7CW_TEXT_DOMAIN); ?></h4>
                            <div class="cf7cw-field-group">
                                <?php
                                $personal_fields = array('full-name', 'birth-date', 'gender');
                                foreach ($personal_fields as $field):
                                    $value = get_post_meta($entry->ID, $field, true);
                                    if (!empty($value)):
                                ?>
                                    <div class="cf7cw-field">
                                        <label><?php echo esc_html($field_labels[$field]); ?></label>
                                        <span><?php echo esc_html($value); ?></span>
                                    </div>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            </div>
                        </div>

                        <!-- Contact Information -->
                        <div class="cf7cw-section">
                            <h4><?php _e('Contact Information', CF7CW_TEXT_DOMAIN); ?></h4>
                            <div class="cf7cw-field-group">
                                <?php
                                $contact_fields = array('phone-number', 'email-address', 'address', 'country-residence');
                                foreach ($contact_fields as $field):
                                    $value = get_post_meta($entry->ID, $field, true);
                                    if (!empty($value)):
                                ?>
                                    <div class="cf7cw-field">
                                        <label><?php echo esc_html($field_labels[$field]); ?></label>
                                        <span><?php echo esc_html($value); ?></span>
                                    </div>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            </div>
                        </div>

                        <!-- Government ID -->
                        <div class="cf7cw-section">
                            <h4><?php _e('Government ID', CF7CW_TEXT_DOMAIN); ?></h4>
                            <div class="cf7cw-field-group">
                                <?php
                                $id_fields = array('identification-number', 'issuing-country', 'expiration-date', 'id-number', 'passport-number', 'place-issue', 'date-issue');
                                foreach ($id_fields as $field):
                                    $value = get_post_meta($entry->ID, $field, true);
                                    if (!empty($value)):
                                ?>
                                    <div class="cf7cw-field">
                                        <label><?php echo esc_html($field_labels[$field]); ?></label>
                                        <span><?php echo esc_html($value); ?></span>
                                    </div>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            </div>
                        </div>

                        <!-- Emergency Contact -->
                        <div class="cf7cw-section">
                            <h4><?php _e('Emergency Contact', CF7CW_TEXT_DOMAIN); ?></h4>
                            <div class="cf7cw-field-group">
                                <?php
                                $emergency_fields = array('contact-name', 'contact-relationship', 'contact-number', 'contact-email');
                                foreach ($emergency_fields as $field):
                                    $value = get_post_meta($entry->ID, $field, true);
                                    if (!empty($value)):
                                ?>
                                    <div class="cf7cw-field">
                                        <label><?php echo esc_html($field_labels[$field]); ?></label>
                                        <span><?php echo esc_html($value); ?></span>
                                    </div>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            </div>
                        </div>

                        <!-- Professional Information -->
                        <div class="cf7cw-section">
                            <h4><?php _e('Professional Information', CF7CW_TEXT_DOMAIN); ?></h4>
                            <div class="cf7cw-field-group">
                                <?php
                                $professional_fields = array('occupation', 'employer', 'professional-aff');
                                foreach ($professional_fields as $field):
                                    $value = get_post_meta($entry->ID, $field, true);
                                    if (!empty($value)):
                                ?>
                                    <div class="cf7cw-field">
                                        <label><?php echo esc_html($field_labels[$field]); ?></label>
                                        <span><?php echo esc_html($value); ?></span>
                                    </div>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            </div>
                        </div>

                        <!-- File Uploads -->
                        <div class="cf7cw-section">
                            <h4><?php _e('Uploaded Files', CF7CW_TEXT_DOMAIN); ?></h4>
                            <div class="cf7cw-field-group">
                                <?php
                                $file_fields = array('occupation-file', 'signature-file');
                                foreach ($file_fields as $field):
                                    $attachment_id = get_post_meta($entry->ID, $field, true);
                                    if (!empty($attachment_id)):
                                        $file_url = wp_get_attachment_url($attachment_id);
                                        $file_name = basename(get_attached_file($attachment_id));
                                ?>
                                    <div class="cf7cw-field">
                                        <label><?php echo esc_html($field_labels[$field]); ?></label>
                                        <a href="<?php echo esc_url($file_url); ?>" target="_blank" class="cf7cw-file-link">
                                            <?php echo esc_html($file_name); ?>
                                        </a>
                                    </div>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>

    <div class="cf7cw-security-footer">
        <p><?php _e('This page is secured. Screenshots, copying, and printing are disabled.', CF7CW_TEXT_DOMAIN); ?></p>
    </div>
</div>

<style>
.cf7cw-submissions-container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
}

.cf7cw-submissions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.cf7cw-submissions-header h2 {
    color: #333;
    margin: 0;
}

.cf7cw-session-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.cf7cw-session-timer {
    background: #fff3cd;
    color: #856404;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
}

.cf7cw-logout-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.cf7cw-logout-btn:hover {
    background: #c82333;
}

.cf7cw-submission-card {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.cf7cw-submission-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cf7cw-submission-header h3 {
    margin: 0;
    font-size: 20px;
}

.cf7cw-submission-date {
    font-size: 14px;
    opacity: 0.9;
}

.cf7cw-submission-content {
    padding: 30px;
}

.cf7cw-submission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.cf7cw-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.cf7cw-section h4 {
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.cf7cw-field {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.cf7cw-field:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.cf7cw-field label {
    font-weight: bold;
    color: #555;
    flex: 0 0 40%;
    font-size: 14px;
}

.cf7cw-field span {
    flex: 1;
    text-align: right;
    color: #333;
    word-break: break-word;
}

.cf7cw-file-link {
    color: #667eea;
    text-decoration: none;
    font-weight: bold;
}

.cf7cw-file-link:hover {
    text-decoration: underline;
}

.cf7cw-no-submissions {
    text-align: center;
    padding: 60px 20px;
    background: #f8f9fa;
    border-radius: 10px;
    color: #666;
}

.cf7cw-security-footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    background: #fff3cd;
    border-radius: 8px;
    color: #856404;
}

@media (max-width: 768px) {
    .cf7cw-submissions-container {
        margin: 10px;
        padding: 15px;
    }

    .cf7cw-submissions-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .cf7cw-submission-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .cf7cw-submission-content {
        padding: 20px;
    }

    .cf7cw-field {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .cf7cw-field span {
        text-align: left;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Session timer
    var sessionTimeout = <?php echo isset($_SESSION['cf7cw_login_time']) ? (900 - (time() - $_SESSION['cf7cw_login_time'])) : 900; ?>;
    var timerInterval;
    
    function updateTimer() {
        var minutes = Math.floor(sessionTimeout / 60);
        var seconds = sessionTimeout % 60;
        
        $('#cf7cw-session-timer').text(
            '<?php _e('Session expires in:', CF7CW_TEXT_DOMAIN); ?> ' + 
            minutes + ':' + (seconds < 10 ? '0' : '') + seconds
        );
        
        if (sessionTimeout <= 0) {
            clearInterval(timerInterval);
            alert('<?php _e('Your session has expired. You will be redirected to the login page.', CF7CW_TEXT_DOMAIN); ?>');
            window.location.href = '<?php echo home_url('/form-login/'); ?>';
        }
        
        sessionTimeout--;
    }
    
    updateTimer();
    timerInterval = setInterval(updateTimer, 1000);
    
    // Logout button
    $('#cf7cw-logout-btn').on('click', function() {
        if (confirm('<?php _e('Are you sure you want to logout?', CF7CW_TEXT_DOMAIN); ?>')) {
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'cf7cw_logout',
                    nonce: '<?php echo wp_create_nonce('cf7cw_logout_nonce'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        window.location.href = response.data.redirect;
                    }
                }
            });
        }
    });
});
</script>

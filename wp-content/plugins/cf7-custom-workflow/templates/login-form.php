<?php
/**
 * Login Form Template
 */

if (!defined('ABSPATH')) {
    exit;
}

$user_id = isset($atts['user_id']) ? intval($atts['user_id']) : 0;
?>

<div class="cf7cw-login-container">
    <div class="cf7cw-login-form">
        <div class="cf7cw-login-header">
            <h2><?php _e('Access Your Submissions', CF7CW_TEXT_DOMAIN); ?></h2>
            <p><?php _e('Enter your login key to view your form submissions', CF7CW_TEXT_DOMAIN); ?></p>
        </div>

        <form id="cf7cw-login-form" method="post">
            <?php wp_nonce_field('cf7cw_login_nonce', 'cf7cw_login_nonce'); ?>
            
            <div class="cf7cw-form-group">
                <label for="cf7cw-user-id"><?php _e('User ID', CF7CW_TEXT_DOMAIN); ?></label>
                <input type="number" 
                       id="cf7cw-user-id" 
                       name="user_id" 
                       value="<?php echo esc_attr($user_id); ?>" 
                       required 
                       <?php echo $user_id ? 'readonly' : ''; ?>>
            </div>

            <div class="cf7cw-form-group">
                <label for="cf7cw-login-key"><?php _e('Login Key', CF7CW_TEXT_DOMAIN); ?></label>
                <input type="text" 
                       id="cf7cw-login-key" 
                       name="login_key" 
                       placeholder="<?php _e('Enter your login key', CF7CW_TEXT_DOMAIN); ?>" 
                       required>
            </div>

            <div class="cf7cw-form-group">
                <button type="submit" class="cf7cw-login-btn">
                    <?php _e('Access Submissions', CF7CW_TEXT_DOMAIN); ?>
                </button>
            </div>
        </form>

        <div id="cf7cw-login-message" class="cf7cw-message" style="display: none;"></div>

        <div class="cf7cw-security-notice">
            <h4><?php _e('Security Notice', CF7CW_TEXT_DOMAIN); ?></h4>
            <ul>
                <li><?php _e('Your session will expire after 15 minutes of inactivity', CF7CW_TEXT_DOMAIN); ?></li>
                <li><?php _e('Do not share your login key with anyone', CF7CW_TEXT_DOMAIN); ?></li>
                <li><?php _e('Screenshots and copying are disabled for security', CF7CW_TEXT_DOMAIN); ?></li>
            </ul>
        </div>
    </div>
</div>

<style>
.cf7cw-login-container {
    max-width: 500px;
    margin: 40px auto;
    padding: 20px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.cf7cw-login-header {
    text-align: center;
    margin-bottom: 30px;
}

.cf7cw-login-header h2 {
    color: #333;
    margin-bottom: 10px;
}

.cf7cw-login-header p {
    color: #666;
    font-size: 14px;
}

.cf7cw-form-group {
    margin-bottom: 20px;
}

.cf7cw-form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #333;
}

.cf7cw-form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.cf7cw-form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.cf7cw-login-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.cf7cw-login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.cf7cw-login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.cf7cw-message {
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
    text-align: center;
}

.cf7cw-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.cf7cw-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.cf7cw-security-notice {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 20px;
    margin-top: 30px;
    border-radius: 5px;
}

.cf7cw-security-notice h4 {
    color: #856404;
    margin-bottom: 10px;
}

.cf7cw-security-notice ul {
    margin: 0;
    padding-left: 20px;
}

.cf7cw-security-notice li {
    color: #856404;
    margin-bottom: 5px;
}

@media (max-width: 600px) {
    .cf7cw-login-container {
        margin: 20px 10px;
        padding: 15px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    $('#cf7cw-login-form').on('submit', function(e) {
        e.preventDefault();
        
        var $form = $(this);
        var $button = $form.find('.cf7cw-login-btn');
        var $message = $('#cf7cw-login-message');
        
        // Disable button and show loading
        $button.prop('disabled', true).text('<?php _e('Logging in...', CF7CW_TEXT_DOMAIN); ?>');
        $message.hide();
        
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'cf7cw_login',
                user_id: $('#cf7cw-user-id').val(),
                login_key: $('#cf7cw-login-key').val(),
                nonce: $('#cf7cw_login_nonce').val()
            },
            success: function(response) {
                if (response.success) {
                    $message.removeClass('error').addClass('success').text(response.data.message).show();
                    
                    // Redirect after 2 seconds
                    setTimeout(function() {
                        window.location.href = response.data.redirect;
                    }, 2000);
                } else {
                    $message.removeClass('success').addClass('error').text(response.data.message).show();
                    $button.prop('disabled', false).text('<?php _e('Access Submissions', CF7CW_TEXT_DOMAIN); ?>');
                }
            },
            error: function() {
                $message.removeClass('success').addClass('error').text('<?php _e('An error occurred. Please try again.', CF7CW_TEXT_DOMAIN); ?>').show();
                $button.prop('disabled', false).text('<?php _e('Access Submissions', CF7CW_TEXT_DOMAIN); ?>');
            }
        });
    });
});
</script>

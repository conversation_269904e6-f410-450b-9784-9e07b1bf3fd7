<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Submission Confirmation</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f4f4f4;
    }

    .container {
      max-width: 600px;
      margin: 20px auto;
      background: #ffffff;
      border-radius: 10px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }

    .header h1 {
      font-size: 24px;
      margin-bottom: 10px;
    }

    .checkmark {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      margin: 0 auto 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 30px;
    }

    .content {
      padding: 40px 30px;
    }

    .message {
      font-size: 16px;
      margin-bottom: 30px;
      color: #555;
    }

    .credentials-section {
      background: #f8f9fa;
      border-left: 4px solid #667eea;
      padding: 25px;
      margin: 25px 0;
      border-radius: 5px;
    }

    .credential-item {
      margin-bottom: 20px;
    }

    .credential-label {
      font-weight: bold;
      color: #333;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 8px;
    }

    .credential-value {
      background: #fff;
      padding: 12px 15px;
      border: 2px solid #e9ecef;
      border-radius: 5px;
      font-family: 'Courier New', monospace;
      font-size: 16px;
      font-weight: bold;
      color: #2c3e50;
      word-break: break-all;
    }

    .qr-section {
      text-align: center;
      margin: 30px 0;
      padding: 25px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .qr-placeholder {
      width: 200px;
      height: 200px;
      background: #fff;
      border: 2px dashed #ccc;
      margin: 0 auto 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
      font-size: 14px;
      border-radius: 8px;
    }

    .access-button {
      display: inline-block;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 12px 30px;
      text-decoration: none;
      border-radius: 25px;
      font-weight: bold;
      margin-top: 15px;
      transition: transform 0.3s ease;
    }

    .access-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .security-warning {
      background: #fff3cd;
      border-left: 4px solid #ffc107;
      padding: 20px;
      margin: 25px 0;
      border-radius: 5px;
    }

    .warning-icon {
      color: #856404;
      font-size: 20px;
      margin-right: 10px;
    }

    .security-text {
      color: #856404;
      font-weight: 500;
    }

    .footer {
      background: #f8f9fa;
      padding: 25px;
      text-align: center;
      color: #666;
      font-size: 14px;
      border-top: 1px solid #e9ecef;
    }

    .divider {
      height: 2px;
      background: linear-gradient(to right, transparent, #667eea, transparent);
      margin: 30px 0;
    }

    @media (max-width: 600px) {
      .container {
        margin: 10px;
        border-radius: 5px;
      }

      .content {
        padding: 30px 20px;
      }

      .credentials-section {
        padding: 20px 15px;
      }

      .qr-placeholder {
        width: 150px;
        height: 150px;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <!-- Header -->
    <div class="header">
      <div class="checkmark">✓</div>
      <h1>Submission Successful!</h1>
      <p>Your submission has been received and processed</p>
    </div>

    <!-- Main Content -->
    <div class="content">
      <p class="message">
        Thank you for your submission, [USER_NAME]. Below are your login credentials and QR code to access your submissions. Please
        keep this information secure.
      </p>

      <div class="divider"></div>

      <!-- Login Credentials -->
      <div class="credentials-section">
        <h3 style="margin-bottom: 20px; color: #333;">Access Credentials</h3>

        <div class="credential-item">
          <div class="credential-label">Login Key</div>
          <div class="credential-value" id="loginKey">[LOGIN_KEY]</div>
        </div>
      </div>

      <!-- QR Code Section -->
      <div class="qr-section">
        <h3 style="margin-bottom: 15px; color: #333;">Quick Access</h3>
        <div class="qr-placeholder">
          QR Code Attached
        </div>
        <p style="color: #666; margin-bottom: 10px;">Scan the QR code attached to this email or click the button below</p>
        <a href="[QR_URL]" class="access-button">Access Your Submissions</a>
      </div>

      <!-- Security Warning -->
      <div class="security-warning">
        <div style="display: flex; align-items: flex-start;">
          <span class="warning-icon">⚠️</span>
          <div>
            <strong class="security-text">Important Security Notice</strong>
            <p class="security-text" style="margin-top: 5px;">
              Do not share your login key with anyone. Keep this information confidential and secure.
              This key provides access to your personal submissions and data. Your session will expire after 15 minutes of inactivity.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="footer">
      <p>If you have any questions or need assistance, please contact our support team.</p>
      <p style="margin-top: 10px; font-size: 12px; color: #999;">
        This is an automated message from [SITE_NAME]. Please do not reply to this email.
      </p>
    </div>
  </div>
</body>

</html>

<?php
/*
Plugin Name: CF7 Custom Workflow
Description: Enhances Contact Form 7 with duplicate prevention, QR login, custom post types, and secure user access.
Version: 1.0.0
Author: Custom Development
Text Domain: cf7-custom-workflow
*/

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('CF7CW_PLUGIN_URL', plugin_dir_url(__FILE__));
define('CF7CW_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('CF7CW_VERSION', '1.0.0');
define('CF7CW_TEXT_DOMAIN', 'cf7-custom-workflow');

// Main plugin class
class CF7_Custom_Workflow {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('CF7_Custom_Workflow', 'uninstall'));
    }
    
    public function init() {
        // Check if Contact Form 7 is active
        if (!class_exists('WPCF7_ContactForm')) {
            add_action('admin_notices', array($this, 'cf7_missing_notice'));
            return;
        }
        
        // Load plugin files
        $this->load_dependencies();
        
        // Initialize components
        $this->init_hooks();
    }
    
    private function load_dependencies() {
        require_once CF7CW_PLUGIN_PATH . 'includes/class-cf7cw-cpt.php';
        require_once CF7CW_PLUGIN_PATH . 'includes/class-cf7cw-validation.php';
        require_once CF7CW_PLUGIN_PATH . 'includes/class-cf7cw-qr-generator.php';
        require_once CF7CW_PLUGIN_PATH . 'includes/class-cf7cw-login-system.php';
        require_once CF7CW_PLUGIN_PATH . 'includes/class-cf7cw-email-handler.php';
        require_once CF7CW_PLUGIN_PATH . 'includes/class-cf7cw-security.php';
        require_once CF7CW_PLUGIN_PATH . 'includes/class-cf7cw-admin.php';
        require_once CF7CW_PLUGIN_PATH . 'includes/class-cf7cw-form-handler.php';
    }
    
    private function init_hooks() {
        // Initialize all components
        new CF7CW_CPT();
        new CF7CW_Validation();
        new CF7CW_QR_Generator();
        new CF7CW_Login_System();
        new CF7CW_Email_Handler();
        new CF7CW_Security();
        new CF7CW_Admin();
        new CF7CW_Form_Handler();
        
        // Add plugin action links
        add_filter('plugin_action_links_' . plugin_basename(__FILE__), array($this, 'plugin_action_links'));
    }
    
    public function activate() {
        // Create database tables
        $this->create_database_tables();
        
        // Create login page
        $this->create_login_page();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    public function deactivate() {
        // Clean up scheduled events
        wp_clear_scheduled_hook('cf7cw_cleanup_expired_sessions');
        wp_clear_scheduled_hook('cf7cw_cleanup_expired_keys');
    }
    
    public static function uninstall() {
        global $wpdb;
        
        // Drop custom tables
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}cf7cw_entry_keys");
        $wpdb->query("DROP TABLE IF EXISTS {$wpdb->prefix}cf7cw_user_sessions");
        
        // Delete custom post type posts
        $posts = get_posts(array(
            'post_type' => 'form_entries',
            'numberposts' => -1,
            'post_status' => 'any'
        ));
        
        foreach ($posts as $post) {
            wp_delete_post($post->ID, true);
        }
        
        // Delete login page
        $login_page = get_page_by_path('form-login');
        if ($login_page) {
            wp_delete_post($login_page->ID, true);
        }
        
        // Delete plugin options
        delete_option('cf7cw_settings');
        delete_option('cf7cw_email_templates');
    }
    
    private function create_database_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Entry keys table
        $table_name = $wpdb->prefix . 'cf7cw_entry_keys';
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            login_key varchar(255) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            expires_at datetime NOT NULL,
            is_active tinyint(1) DEFAULT 1,
            PRIMARY KEY (id),
            UNIQUE KEY user_id (user_id),
            KEY login_key (login_key)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // User sessions table
        $sessions_table = $wpdb->prefix . 'cf7cw_user_sessions';
        $sessions_sql = "CREATE TABLE $sessions_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            session_token varchar(255) NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            expires_at datetime NOT NULL,
            ip_address varchar(45),
            user_agent text,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY session_token (session_token)
        ) $charset_collate;";
        
        dbDelta($sessions_sql);
    }
    
    private function create_login_page() {
        // Check if login page already exists
        $login_page = get_page_by_path('form-login');
        
        if (!$login_page) {
            $page_data = array(
                'post_title' => 'Form Login',
                'post_name' => 'form-login',
                'post_content' => '[cf7cw_login_form]',
                'post_status' => 'publish',
                'post_type' => 'page',
                'post_author' => 1,
                'comment_status' => 'closed',
                'ping_status' => 'closed'
            );
            
            wp_insert_post($page_data);
        }
    }
    
    public function cf7_missing_notice() {
        echo '<div class="notice notice-error"><p>';
        echo __('CF7 Custom Workflow requires Contact Form 7 to be installed and activated.', CF7CW_TEXT_DOMAIN);
        echo '</p></div>';
    }
    
    public function plugin_action_links($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=cf7cw-settings') . '">' . __('Settings', CF7CW_TEXT_DOMAIN) . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }
}

// Initialize the plugin
CF7_Custom_Workflow::get_instance();
